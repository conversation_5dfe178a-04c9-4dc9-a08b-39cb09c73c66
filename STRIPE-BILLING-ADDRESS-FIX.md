# Stripe Billing Address Error Fix

## Problem Identified

When selecting <PERSON><PERSON> as the payment method, users were getting this error:

```
You specified "never" for fields.billing_details.address.state when creating the payment Element, but did not pass params.billing_details.address.state when calling stripe.createPaymentMethod(). If you opt out of collecting data via the payment Element using the fields option, the data must be passed in when calling stripe.createPaymentMethod().
```

## Root Cause

The issue had **two parts**:

### 1. Missing Fields in Custom Template
The **custom Divi checkout form template** (`wp-content/themes/Divi/woocommerce/checkout/form-billing.php`) was missing critical billing address fields:
- **`billing_state` field** - Required for Italy (labeled as "Province")
- **`billing_country` field** - Required for all Stripe payments

### 2. Functions.php Override Conflict
The **Divi functions.php** had a filter that was setting `billing_state` as **not required**, conflicting with WooCommerce's default requirement for Italy:

```php
// In functions.php line 9221 - THE PROBLEM:
$address_fields['billing_state']['required'] = false;
```

### Why This Happened

- **WooCommerce core** defines Italy (`IT`) as requiring a state field (Province)
- **Functions.php override** was setting billing_state as not required
- **Custom Divi template** wasn't rendering the state/country fields at all
- **Stripe's `createPaymentMethod()`** API requires billing address state when it's marked as required in WooCommerce
- This created a conflict where Stripe expected the field but it wasn't being collected

## Solution Applied

### 1. Fixed Functions.php Field Requirements

**Fixed the billing field requirements** in `wp-content/themes/Divi/functions.php`:

```php
// BEFORE (line 9221):
$address_fields['billing_state']['required'] = false;

// AFTER (line 9221):
$address_fields['billing_state']['required'] = true;  // Fix for Stripe: required for Italy
```

### 2. Added Missing Billing Fields to Template

**Added to the visible billing section:**
```php
// Add the missing billing_country field (required for Stripe payments)
if (isset($checkout->checkout_fields['billing']['billing_country'])) {
    woocommerce_form_field( 'billing_country', $checkout->checkout_fields['billing']['billing_country'], $checkout->get_value( 'billing_country') );
}
// Add the missing billing_state field (required for Stripe payments in Italy)
if (isset($checkout->checkout_fields['billing']['billing_state'])) {
    woocommerce_form_field( 'billing_state', $checkout->checkout_fields['billing']['billing_state'], $checkout->get_value( 'billing_state') );
}
```

**Added to the hidden billing section (when billing details not required):**
```php
// Add the missing billing_country field with default value for hidden section
if (isset($checkout->checkout_fields['billing']['billing_country'])) {
    woocommerce_form_field( 'billing_country', $checkout->checkout_fields['billing']['billing_country'], 'IT' );
}
// Add the missing billing_state field with default value for hidden section
if (isset($checkout->checkout_fields['billing']['billing_state'])) {
    woocommerce_form_field( 'billing_state', $checkout->checkout_fields['billing']['billing_state'], 'MI' );
}
```

### 2. Default Values Used

- **Country**: `IT` (Italy) - appropriate for the Italian site
- **State**: `MI` (Milano) - a valid Italian province code

## Files Modified

1. **`wp-content/themes/Divi/functions.php`** - Fixed billing_state field requirement
2. **`wp-content/themes/Divi/woocommerce/checkout/form-billing.php`** - Added missing billing fields

## Testing

After applying this fix:

1. ✅ **Stripe payments should work** without the billing address error
2. ✅ **Country and Province fields** will be available for users to fill
3. ✅ **Hidden billing sections** will have default values to satisfy Stripe requirements
4. ✅ **Existing functionality** remains unchanged

## Technical Details

### WooCommerce Italy Configuration
```php
'IT' => array(
    'state' => array(
        'required' => true,
        'label'    => __( 'Province', 'woocommerce' ),
    ),
),
```

### Stripe Requirements
- Stripe's `createPaymentMethod()` requires billing address data when fields are marked as required
- The error occurred because the form wasn't collecting the state data, but Stripe expected it

## Prevention

To prevent similar issues in the future:

1. **Always include standard WooCommerce address fields** in custom checkout templates
2. **Test payment methods** thoroughly when customizing checkout forms
3. **Check country-specific requirements** for address fields
4. **Use WooCommerce's standard field rendering** when possible

## Verification Steps

1. Go to checkout page
2. Select a product and proceed to checkout
3. Choose Stripe as payment method
4. Verify that Country and Province fields are visible
5. Complete a test payment to confirm the error is resolved
