<?php
/**
 * Plugin Name: NAM Export
 * Plugin URI:
 * Description: Export orders from WooCommerce
 * Author: Playground
 * Author URI: https://playground.it
 * Version: 1.0.0
 *
 * Copyright: (c) 2018 Playground srl
 *
 * License: GNU General Public License v3.0
 * License URI: http://www.gnu.org/licenses/gpl-3.0.html
 *
 * @package     nam-export
 * <AUTHOR>
 * @Category    Plugin
 * @copyright   Copyright (c) 2018 Playground srl
 */

if (!defined('ABSPATH')) {
    exit;
}

class NamExport
{
    private static $instance = null;

    public static function instance()
    {
        if (null === self::$instance) {
            self::$instance = new NamExport();
        }
        return self::$instance;
    }

    private $csv_separator = ';';
    private $csv_line_separator = "\n";
    private $meta_box_export_title = 'Export Orders';
    private $button_export_text = 'Export CSV';
    private $export_uri = '/nam-export';
    private $export_uri_product_id = 'product_id';

    private function __construct()
    {
        add_action('add_meta_boxes', [$this, 'add_meta_box']);
        add_action('template_redirect', [$this, 'template_redirect']);
        add_action('admin_notices', [$this, 'check_debug_log_size']);
    }

    public function template_redirect()
    {
        // Safety checks to prevent unauthorized access and errors
        if (!isset($_SERVER['REQUEST_URI'])) {
            return;
        }

        if (0 === strpos($_SERVER['REQUEST_URI'], $this->export_uri) &&
            current_user_can('administrator') &&
            isset($_GET[$this->export_uri_product_id]) &&
            !empty($_GET[$this->export_uri_product_id])) {

            // Sanitize the product_id
            $product_id = intval($_GET[$this->export_uri_product_id]);
            if ($product_id > 0) {
                $this->downloadCSV($product_id);
            }
        }
    }

    public function add_meta_box()
    {
        add_meta_box(
            'woocommerce-product-export',
            __($this->meta_box_export_title),
            [$this, 'meta_box_export_content'],
            'product',
            'side',
            'default'
        );
    }

    public function meta_box_export_content()
    {
        $url = $this->export_uri . '?' . $this->export_uri_product_id . '=' . get_the_ID();
        ?>
        <a class="button button-primary button-large" href="<?= $url ?>">
            <?= __($this->button_export_text) ?>
        </a>
        <?php
    }

    private function createCsvData($product_id)
    {
        $csv_data = [];

        // Validate product_id
        if (empty($product_id) || !is_numeric($product_id)) {
            error_log('NAM Export: Invalid product_id provided: ' . $product_id);
            return false;
        }

        // Get the related orders with memory-efficient approach
        // Limit to 1000 orders to prevent memory issues
        $orders = wc_get_orders([
            'limit' => 1000,
            'status' => ['completed', 'processing', 'on-hold'],
            'return' => 'ids'
        ]);

        if (empty($orders)) {
            return false;
        }

        $filtered_orders = [];
        foreach ($orders as $order_id) {
            try {
                $order = wc_get_order($order_id);
                if (!$order) {
                    continue;
                }

                $items = $order->get_items();
                foreach ($items as $item) {
                    if ($product_id == $item->get_product_id()) {
                        $filtered_orders[] = $order;
                        break;
                    }
                }
            } catch (Exception $e) {
                error_log('NAM Export: Error processing order ' . $order_id . ': ' . $e->getMessage());
                continue;
            }
        }

        $orders = $filtered_orders;


        // Get the form data with error handling
        $custom_fields = [];
        try {
            $product = wc_get_product($product_id);
            if (!$product) {
                error_log('NAM Export: Product not found: ' . $product_id);
                return false;
            }

            $gravity_form_data = $product->get_meta('_gravity_form_data', true);

            if (is_array($gravity_form_data) && isset($gravity_form_data['id']) && is_numeric($gravity_form_data['id'])) {
                if (class_exists('RGFormsModel')) {
                    $form_meta = RGFormsModel::get_form_meta($gravity_form_data['id']);
                    if (is_array($form_meta) && isset($form_meta['fields'])) {
                        $custom_fields = array_reduce($form_meta['fields'], function ($carry, $field) {
                            if (is_object($field) && isset($field->id, $field->label) &&
                                !($field instanceof GF_Field_HiddenProduct || $field instanceof GF_Field_Hidden)) {
                                $carry[$field->id] = $field->label;
                            }
                            return $carry;
                        }, []);
                    }
                } else {
                    error_log('NAM Export: Gravity Forms not available');
                }
            }
        } catch (Exception $e) {
            error_log('NAM Export: Error getting form data: ' . $e->getMessage());
        }


        // Headers
        $header = [
            'Numero ordine',
            'Stato Ordine',
            'Data Ordine',
            'Titolo',
            'Nome',
            'Cognome',
            'Email',
            'Ragione Sociale',
            'Codice Fiscale',
            'P.IVA',
            'Indirizzo',
            'CAP',
            'Città',
            'Pubblica Amministrazione',
            'Email per fattura',
            'PEC o SDI',
            'Metodo di pagamento',
        ];

        foreach ($custom_fields as $custom_field) {
            $header[] = $custom_field;
        }

        $csv_data[] = $header;


        // Contents
        if (count($orders)) {
            foreach ($orders as $order) {
                try {
                    $row = [];

                    // Standard fields with safe access
                    $data = $order->get_data();
                    if (!is_array($data)) {
                        continue;
                    }

                    $row[] = isset($data['id']) ? $data['id'] : '';
                    $row[] = isset($data['status']) ? $data['status'] : '';
                    $row[] = isset($data['date_created']) && is_object($data['date_created']) ?
                             $data['date_created']->date_i18n('Y-m-d') : '';
                    $row[] = get_post_meta($order->get_id(), '_billing_title', true);
                    $row[] = isset($data['billing']['first_name']) ? $data['billing']['first_name'] : '';
                    $row[] = isset($data['billing']['last_name']) ? $data['billing']['last_name'] : '';
                    $row[] = isset($data['billing']['email']) ? $data['billing']['email'] : '';
                    $row[] = isset($data['billing']['company']) ? $data['billing']['company'] : '';
                    $row[] = get_post_meta($order->get_id(), '_billing_VAT', true);
                    $row[] = get_post_meta($order->get_id(), '_billing_CF', true);
                    $address = '';
                    if (isset($data['billing']['address_1']) || isset($data['billing']['address_2'])) {
                        $address = trim((isset($data['billing']['address_1']) ? $data['billing']['address_1'] : '') .
                                      ' ' . (isset($data['billing']['address_2']) ? $data['billing']['address_2'] : ''));
                    }
                    $row[] = $address;
                    $row[] = isset($data['billing']['postcode']) ? $data['billing']['postcode'] : '';
                    $row[] = isset($data['billing']['city']) ? $data['billing']['city'] : '';
                    $row[] = get_post_meta($order->get_id(), '_billing_pa', true);
                    $row[] = get_post_meta($order->get_id(), '_billing_email_fattura', true);
                    $row[] = get_post_meta($order->get_id(), '_billing_sdi_pec', true);
                    $row[] = isset($data['payment_method_title']) ? $data['payment_method_title'] : '';

                    // Custom fields with error handling
                    if (count($custom_fields)) {
                        try {
                            $items = $order->get_items();
                            $item = reset($items);

                            if ($item) {
                                $meta_data_items = $item->get_meta_data();
                                $entry_id = null;
                                $entry = null;

                                foreach ($meta_data_items as $meta) {
                                    if (isset($meta->key) && $meta->key == '_gravity_forms_history' &&
                                        isset($meta->value['_gravity_form_linked_entry_id'])) {
                                        $entry_id = $meta->value['_gravity_form_linked_entry_id'];
                                        break;
                                    }
                                }

                                if ($entry_id && class_exists('GFAPI')) {
                                    $entry = GFAPI::get_entry($entry_id);
                                }

                                if ($entry && !is_wp_error($entry)) {
                                    foreach (array_keys($custom_fields) as $field_id) {
                                        $value = isset($entry[$field_id]) ? $entry[$field_id] : '';

                                        $pipe = strpos($value, '|');
                                        if ($pipe !== false && $pipe > 0) {
                                            $value = substr($value, 0, $pipe);
                                        }

                                        if (isset($custom_fields[$field_id]) &&
                                            in_array(strtolower($custom_fields[$field_id]), ['telefono', 'fax'])) {
                                            $value = "|$value";
                                        }

                                        $row[] = $value;
                                    }
                                } else {
                                    // Fill with empty values if no entry found
                                    for ($i = 0; $i < count($custom_fields); $i++) {
                                        $row[] = '';
                                    }
                                }
                            } else {
                                // Fill with empty values if no items found
                                for ($i = 0; $i < count($custom_fields); $i++) {
                                    $row[] = '';
                                }
                            }
                        } catch (Exception $e) {
                            error_log('NAM Export: Error processing custom fields for order ' . $order->get_id() . ': ' . $e->getMessage());
                            // Fill with empty values on error
                            for ($i = 0; $i < count($custom_fields); $i++) {
                                $row[] = '';
                            }
                        }
                    }

                    // Uppercase conversion with error handling
                    $row = array_map(function($item) {
                        return is_string($item) ? strtoupper($item) : $item;
                    }, $row);

                    $csv_data[] = $row;

                } catch (Exception $e) {
                    error_log('NAM Export: Error processing order ' . $order->get_id() . ': ' . $e->getMessage());
                    continue;
                }
            }
        }

        return $csv_data;
    }

    private function parseCsvData($csv_data)
    {
        foreach ($csv_data as &$row) {
            $row = array_map('utf8_decode', $row);
            $row = array_map(function ($entry) {
                return "\"$entry\"";
            }, $row);
            $row = implode($this->csv_separator, $row);
        }

        return implode($this->csv_line_separator, $csv_data);
    }

    public function downloadCSV($product_id)
    {
        try {
            // Validate product_id
            if (empty($product_id) || !is_numeric($product_id)) {
                error_log('NAM Export: Invalid product_id for download: ' . $product_id);
                wp_die('Invalid product ID');
                return false;
            }

            $product = wc_get_product($product_id);
            if (!$product) {
                error_log('NAM Export: Product not found for download: ' . $product_id);
                wp_die('Product not found');
                return false;
            }

            $default_timezone = date_default_timezone_get();
            date_default_timezone_set('Europe/Rome');
            $title = date('ymdHi') . '-' . preg_replace('/[^A-Za-z0-9\-]/', '_', $product->get_title());
            date_default_timezone_set($default_timezone);

            $csv = $this->createCsvData($product_id);

            if (!$csv || empty($csv)) {
                error_log('NAM Export: No data found for product: ' . $product_id);
                wp_die('No data found for export');
                return false;
            }

            $csv = $this->parseCsvData($csv);

            // Clear any previous output
            if (ob_get_level()) {
                ob_end_clean();
            }

            header('Content-type: application/CSV', true, 200);
            header('Content-Disposition: attachment; filename=' . $title . '.csv');
            header('Pragma: no-cache');
            header('Expires: 0');
            echo $csv;
            exit;

        } catch (Exception $e) {
            error_log('NAM Export: Fatal error in downloadCSV: ' . $e->getMessage());
            wp_die('Export failed: ' . $e->getMessage());
        }
    }

    public function check_debug_log_size()
    {
        // Only show to administrators
        if (!current_user_can('administrator')) {
            return;
        }

        $debug_log_path = WP_CONTENT_DIR . '/debug.log';

        if (file_exists($debug_log_path)) {
            $file_size = filesize($debug_log_path);
            $file_size_mb = round($file_size / 1024 / 1024, 2);

            // Show warning if debug log is larger than 100MB
            if ($file_size > 100 * 1024 * 1024) {
                echo '<div class="notice notice-error is-dismissible">';
                echo '<p><strong>NAM Export Warning:</strong> Your debug.log file is very large (' . $file_size_mb . ' MB) and may be causing memory issues.</p>';
                echo '<p>Consider <a href="' . plugin_dir_url(__FILE__) . 'cleanup-debug-log.php?cleanup_debug=1" target="_blank">cleaning up the debug log</a> or disabling debug logging in production.</p>';
                echo '</div>';
            }
        }
    }
}

NamExport::instance();

