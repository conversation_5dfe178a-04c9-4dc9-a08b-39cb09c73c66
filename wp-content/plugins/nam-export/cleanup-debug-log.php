<?php
/**
 * Debug Log Cleanup Script for NAM Export
 * 
 * This script helps clean up the massive debug.log file that's causing memory issues.
 * Run this script to clear the debug log and prevent memory crashes.
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

class DebugLogCleanup {
    
    public static function cleanup_debug_log() {
        $debug_log_path = WP_CONTENT_DIR . '/debug.log';
        
        if (file_exists($debug_log_path)) {
            $file_size = filesize($debug_log_path);
            $file_size_mb = round($file_size / 1024 / 1024, 2);
            
            echo "<h3>Debug Log Cleanup</h3>";
            echo "<p>Current debug.log size: <strong>{$file_size_mb} MB</strong></p>";
            
            if ($file_size > 50 * 1024 * 1024) { // If larger than 50MB
                echo "<p style='color: red;'>⚠️ Debug log is very large and may be causing memory issues!</p>";
                
                // Create backup of last 1000 lines
                $backup_path = WP_CONTENT_DIR . '/debug-backup-' . date('Y-m-d-H-i-s') . '.log';
                $lines = file($debug_log_path);
                if ($lines && count($lines) > 1000) {
                    $last_lines = array_slice($lines, -1000);
                    file_put_contents($backup_path, implode('', $last_lines));
                    echo "<p>✅ Backup of last 1000 lines saved to: " . basename($backup_path) . "</p>";
                }
                
                // Clear the debug log
                if (file_put_contents($debug_log_path, '')) {
                    echo "<p style='color: green;'>✅ Debug log cleared successfully!</p>";
                } else {
                    echo "<p style='color: red;'>❌ Failed to clear debug log. Check file permissions.</p>";
                }
            } else {
                echo "<p style='color: green;'>✅ Debug log size is acceptable.</p>";
            }
        } else {
            echo "<p>No debug.log file found.</p>";
        }
    }
    
    public static function show_debug_settings() {
        echo "<h3>Current Debug Settings</h3>";
        echo "<ul>";
        echo "<li>WP_DEBUG: " . (defined('WP_DEBUG') && WP_DEBUG ? '<span style="color: red;">ENABLED</span>' : '<span style="color: green;">DISABLED</span>') . "</li>";
        echo "<li>WP_DEBUG_LOG: " . (defined('WP_DEBUG_LOG') && WP_DEBUG_LOG ? '<span style="color: red;">ENABLED</span>' : '<span style="color: green;">DISABLED</span>') . "</li>";
        echo "<li>WP_DEBUG_DISPLAY: " . (defined('WP_DEBUG_DISPLAY') && WP_DEBUG_DISPLAY ? '<span style="color: red;">ENABLED</span>' : '<span style="color: green;">DISABLED</span>') . "</li>";
        echo "</ul>";
        
        if (defined('WP_DEBUG') && WP_DEBUG) {
            echo "<div style='background: #fff3cd; border: 1px solid #ffeaa7; padding: 15px; margin: 10px 0;'>";
            echo "<h4>⚠️ Recommendation for Production</h4>";
            echo "<p>Debug logging is currently enabled, which can cause large log files and memory issues.</p>";
            echo "<p>For production sites, consider adding this to your wp-config.php:</p>";
            echo "<pre style='background: #f8f9fa; padding: 10px; border: 1px solid #dee2e6;'>";
            echo "// Disable debug logging in production\n";
            echo "define('WP_DEBUG', false);\n";
            echo "define('WP_DEBUG_LOG', false);\n";
            echo "define('WP_DEBUG_DISPLAY', false);\n";
            echo "</pre>";
            echo "</div>";
        }
    }
}

// If accessed directly via browser (for testing)
if (isset($_GET['cleanup_debug']) && current_user_can('administrator')) {
    echo "<html><head><title>Debug Log Cleanup</title></head><body>";
    echo "<h1>NAM Export - Debug Log Cleanup</h1>";
    DebugLogCleanup::cleanup_debug_log();
    DebugLogCleanup::show_debug_settings();
    echo "<p><a href='" . admin_url() . "'>← Back to Admin</a></p>";
    echo "</body></html>";
    exit;
}
