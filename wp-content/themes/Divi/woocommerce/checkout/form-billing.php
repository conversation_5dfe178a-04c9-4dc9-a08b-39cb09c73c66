<?php
/**
 * Checkout billing information form
 *
 * This template can be overridden by copying it to yourtheme/woocommerce/checkout/form-billing.php.
 *
 * HOWEVER, on occasion WooCommerce will need to update template files and you
 * (the theme developer) will need to copy the new files to your theme to
 * maintain compatibility. We try to do this as little as possible, but it does
 * happen. When this occurs the version of the template file will be bumped and
 * the readme will list any important changes.
 *
 * @see     https://docs.woocommerce.com/document/template-structure/
 * <AUTHOR>
 * @package WooCommerce/Templates
 * @version 3.0.9
 */

switch (ICL_LANGUAGE_CODE) {

	case "en":
		$check_privacy_user = "<div class='disclaimer'><input id='field_terms' type='checkbox' required name='terms'>
		I accept the <a style='text-decoration:underline;' href='/site-registration-terms/?lang=en' target='_blank'>registration conditions</a> on the site.</div>";
	break;

	default: 
		$check_privacy_user = "<div class='disclaimer'><input id='field_terms' type='checkbox' required name='terms'> Accetto le <a style='text-decoration:underline;' href='/termini-registrazione-al-sito/' target='_blank'>condizioni di registrazione</a> al servizio.</div>";
break;
}


if ( ! defined( 'ABSPATH' ) ) {
	exit; // Exit if accessed directly
}

/** @global WC_Checkout $checkout */

?>
<div class="woocommerce-billing-fields">
	<?php if ( wc_ship_to_billing_address_only() && WC()->cart->needs_shipping() ) : ?>

		<h3><?php _e( 'Billing &amp; Shipping', 'woocommerce' ); ?></h3>

	<?php else : ?>

		<h3><?php _e( 'Dati personali', 'woocommerce' ); ?></h3>

	<?php endif; ?>

	<?php do_action( 'woocommerce_before_checkout_billing_form', $checkout ); ?>

	<div class="woocommerce-billing-fields__field-wrapper">
		<?php
			if ( is_user_logged_in()) :
				woocommerce_form_field( 'billing_title', $checkout->checkout_fields['billing']['billing_title'], $checkout->get_value( 'billing_title') );
				woocommerce_form_field( 'billing_first_name', $checkout->checkout_fields['billing']['billing_first_name'], $checkout->get_value( 'billing_first_name') );
				woocommerce_form_field( 'billing_last_name', $checkout->checkout_fields['billing']['billing_last_name'], $checkout->get_value( 'billing_last_name') );
				woocommerce_form_field( 'billing_email', $checkout->checkout_fields['billing']['billing_email'], $checkout->get_value( 'billing_email') );
				woocommerce_form_field( 'billing_em_ver', $checkout->checkout_fields['billing']['billing_em_ver'], $checkout->get_value( 'billing_em_ver') );
			else:
				woocommerce_form_field( 'billing_title', $checkout->checkout_fields['billing']['billing_title'], $checkout->get_value( 'billing_title'));
				woocommerce_form_field( 'billing_first_name', $checkout->checkout_fields['billing']['billing_first_name'], $checkout->get_value( 'billing_first_name') );
				woocommerce_form_field( 'billing_last_name', $checkout->checkout_fields['billing']['billing_last_name'], $checkout->get_value( 'billing_last_name'));
				woocommerce_form_field( 'billing_email', $checkout->checkout_fields['billing']['billing_email'], $checkout->get_value( 'billing_email'));
				woocommerce_form_field( 'billing_em_ver', $checkout->checkout_fields['billing']['billing_em_ver'], $checkout->get_value( 'billing_em_ver'));
			endif;
		?>
		
		<?php
			if ( ! is_user_logged_in() && $checkout->is_registration_enabled() ) :
				woocommerce_form_field( 'account_password', $checkout->checkout_fields['account']['account_password'], $checkout->get_value( 'account_password') );
				woocommerce_form_field( 'account_password2', $checkout->checkout_fields['account']['account_password2'], $checkout->get_value( 'account_password2') );
				echo $check_privacy_user;
				echo"<div class='disclaimer'>Questi dati serviranno per creare il profilo personale e per accedervi in futuro.</div>";
			endif;
		?>
		
		<?php
		
		foreach( WC()->cart->get_cart() as $cart_item ){
			if( version_compare( WC_VERSION, '3.0', '<' ) ){
				$product_id = $cart_item['data']->id;
			} else {
				$product_id = $cart_item['data']->get_id();
			}
		}
	
		
		$fa = get_field('fatturazione_attivata', $product_id);
		
		
		if($fa=="Si"): 
		?>
		
		<div class="dati_fatturazione" style="float:left; width:100%">
			<h3><?php _e( 'Billing details', 'woocommerce' ); ?></h3>
			<?php
				woocommerce_form_field( 'billing_company', $checkout->checkout_fields['billing']['billing_company'], $checkout->get_value( 'billing_company') );
				woocommerce_form_field( 'billing_CF', $checkout->checkout_fields['billing']['billing_CF'], $checkout->get_value( 'billing_CF') );
				woocommerce_form_field( 'billing_VAT', $checkout->checkout_fields['billing']['billing_VAT'], $checkout->get_value( 'billing_VAT') );
				woocommerce_form_field( 'billing_address_1', $checkout->checkout_fields['billing']['billing_address_1'], $checkout->get_value( 'billing_address_1') );
				woocommerce_form_field( 'billing_postcode', $checkout->checkout_fields['billing']['billing_postcode'], $checkout->get_value( 'billing_postcode') );
				woocommerce_form_field( 'billing_city', $checkout->checkout_fields['billing']['billing_city'], $checkout->get_value( 'billing_city') );
				// Add the missing billing_country field (required for Stripe payments)
				if (isset($checkout->checkout_fields['billing']['billing_country'])) {
					$country_field = $checkout->checkout_fields['billing']['billing_country'];
					// Force field to be visible (override any hidden setting)
					$country_field['hidden'] = false;
					woocommerce_form_field( 'billing_country', $country_field, $checkout->get_value( 'billing_country') );
				}
				// Add the missing billing_state field (required for Stripe payments in Italy)
				if (isset($checkout->checkout_fields['billing']['billing_state'])) {
					$state_field = $checkout->checkout_fields['billing']['billing_state'];
					// Force field to be visible (override any hidden setting)
					$state_field['hidden'] = false;
					woocommerce_form_field( 'billing_state', $state_field, $checkout->get_value( 'billing_state') );
				}
				woocommerce_form_field( 'billing_sdi_pec', $checkout->checkout_fields['billing']['billing_sdi_pec'], $checkout->get_value( 'billing_sdi_pec') );
				woocommerce_form_field( 'billing_email_fattura', $checkout->checkout_fields['billing']['billing_email_fattura'], $checkout->get_value( 'billing_email_fattura') );
				woocommerce_form_field( 'billing_pa', $checkout->checkout_fields['billing']['billing_pa'], $checkout->get_value( 'billing_pa') );
			?>
		</div>
		<?php 
			else: 
		?>
		<div style="display:none">
			<?php
				woocommerce_form_field( 'billing_company', $checkout->checkout_fields['billing']['billing_company'], '-' );
				woocommerce_form_field( 'billing_CF', $checkout->checkout_fields['billing']['billing_CF'], '-' );
				woocommerce_form_field( 'billing_VAT', $checkout->checkout_fields['billing']['billing_VAT'], '-' );
				woocommerce_form_field( 'billing_address_1', $checkout->checkout_fields['billing']['billing_address_1'], '-' );
				woocommerce_form_field( 'billing_postcode', $checkout->checkout_fields['billing']['billing_postcode'], '0' );
				woocommerce_form_field( 'billing_city', $checkout->checkout_fields['billing']['billing_city'], '-' );
				// Add the missing billing_country field with default value for hidden section
				if (isset($checkout->checkout_fields['billing']['billing_country'])) {
					$country_field = $checkout->checkout_fields['billing']['billing_country'];
					// Force field to be visible even in hidden section (for Stripe)
					$country_field['hidden'] = false;
					woocommerce_form_field( 'billing_country', $country_field, 'IT' );
				}
				// Add the missing billing_state field with default value for hidden section
				if (isset($checkout->checkout_fields['billing']['billing_state'])) {
					$state_field = $checkout->checkout_fields['billing']['billing_state'];
					// Force field to be visible even in hidden section (for Stripe)
					$state_field['hidden'] = false;
					woocommerce_form_field( 'billing_state', $state_field, 'MI' );
				}
				woocommerce_form_field( 'billing_sdi_pec', $checkout->checkout_fields['billing']['billing_sdi_pec'], '-' );
				woocommerce_form_field( 'billing_email_fattura', $checkout->checkout_fields['billing']['billing_email_fattura'], '-' );
				woocommerce_form_field( 'billing_pa', $checkout->checkout_fields['billing']['billing_pa'], '-' );
			?>
		</div>
		<?php
			endif;
		?>
		<?php
			foreach( WC()->cart->get_cart() as $cart_item ){
				if( version_compare( WC_VERSION, '3.0', '<' ) ){
					$product_id = $cart_item['data']->id;	
				} else {
					$product_id = $cart_item['data']->get_id();
				}
			}
				
			$cn = get_field('campo_note', $product_id);
			
			if($cn):
		?>
		<div class="campo-note">
			<h3><?php _e( 'Policy Cancellazione', 'woocommerce' ); ?></h3>
			<p><?php echo $cn; ?></p>
		</div>
		<?php endif; ?>
		
	</div>

	<?php do_action( 'woocommerce_after_checkout_billing_form', $checkout ); ?>
</div>
