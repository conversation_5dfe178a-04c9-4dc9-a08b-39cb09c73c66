# Translation of Plugins - Yoast SEO &#8211; Advanced SEO with real-time guidance and built-in AI - Stable (latest release) in Italian
# This file is distributed under the same license as the Plugins - Yoast SEO &#8211; Advanced SEO with real-time guidance and built-in AI - Stable (latest release) package.
msgid ""
msgstr ""
"PO-Revision-Date: 2025-06-30 10:07:57+0000\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Plural-Forms: nplurals=2; plural=n != 1;\n"
"X-Generator: GlotPress/4.0.1\n"
"Language: it\n"
"Project-Id-Version: Plugins - Yoast SEO &#8211; Advanced SEO with real-time guidance and built-in AI - Stable (latest release)\n"

#: src/llms-txt/application/file/file-failure-notification-presenter.php:49
#: src/llms-txt/user-interface/health-check/file-reports.php:86
msgid "You have activated the Yoast llms.txt feature, but we couldn't generate an llms.txt file, for unknown reasons."
msgstr "Hai attivato la funzione di Yoast che genera il file llms.txt, ma c'è un problema con la sua generazione le cui ragioni non sono chiare."

#. translators: 1,3: expand to opening paragraph tag, 2,4: expand to opening
#. paragraph tag.
#: src/llms-txt/user-interface/health-check/file-reports.php:77
msgid "%1$sYou have activated the Yoast llms.txt feature, but we couldn't generate an llms.txt file.%2$s%3$sIt looks like there aren't sufficient permissions on the web server's filesystem.%4$s"
msgstr "%1$sHai attivato la funzione di Yoast che genera il file llms.txt, ma c'è un problema con la sua generazione. %2$s%3$sSembra che manchino i permessi nel filesystem del server web.%4$s"

#. translators: 1,3,5: expand to opening paragraph tag, 2,4,6: expand to
#. opening paragraph tag.
#: src/llms-txt/user-interface/health-check/file-reports.php:64
msgid "%1$sYou have activated the Yoast llms.txt feature, but we couldn't generate an llms.txt file.%2$s%3$sIt looks like there is an llms.txt file already that wasn't created by Yoast, or the llms.txt file created by Yoast has been edited manually.%4$s%5$sWe don't want to overwrite this file's content, so if you want to let Yoast keep auto-generating the llms.txt file, you can manually delete the existing one. Otherwise, consider disabling the Yoast feature.%6$s"
msgstr "%1$sHai attivato la funzione di Yoast che genera il file llms.txt, ma c'è un problema con la sua generazione. %2$s%3$sSembra sia già presente un file llms.txt file che non è stato creato da Yoast, o che il file llms.txt creato da Yoast è stato modificato a mano. %4$s%5$sNon vogliamo sovrascriverlo. Per questo, se vuoi che Yoast continui la generazione automatica del file llms.txt, devi eliminare manualmente il file esistente. Se preferisci, puoi disattivare questa funzionalità.%6$s"

#: src/llms-txt/application/file/file-failure-notification-presenter.php:55
#: src/llms-txt/user-interface/health-check/file-reports.php:61
#: src/llms-txt/user-interface/health-check/file-reports.php:74
#: src/llms-txt/user-interface/health-check/file-reports.php:85
msgid "Your llms.txt file couldn't be auto-generated"
msgstr "Il file llms.txt non può essere generato automaticamente"

#. translators: %s: Yoast SEO.
#: src/llms-txt/user-interface/health-check/file-reports.php:40
msgid "%s keeps your llms.txt file up-to-date. This helps LLMs access and provide your site's information more easily."
msgstr "%s mantieni il tuo file llms.txt aggiornato. Questo aiuta l'accesso LLMs e fornisce le informazioni sul tuo sito più facilmente."

#. translators: %s: Yoast SEO.
#: src/llms-txt/user-interface/health-check/file-reports.php:34
msgid "Your llms.txt file is auto-generated by %s"
msgstr "Il tuo file llms.txt è generato automaticamente da %s"

#: src/dashboard/user-interface/tracking/setup-steps-tracking-route.php:149
msgid "No valid parameters were passed."
msgstr "Non sono stati passati parametri validi."

#: src/integrations/admin/link-count-columns-integration.php:147
msgid "Number of internal links linking to this post."
msgstr "Numero di link interni che rimandano a questo articolo."

#: src/integrations/admin/link-count-columns-integration.php:139
msgid "Number of outgoing internal links in this post."
msgstr "Numero di link interni in uscita in questo articolo."

#: inc/class-wpseo-rank.php:223 js/dist/externals/dashboardFrontend.js:4
msgid "Not analyzed"
msgstr "Non è stato analizzato"

#. translators: %s: Yoast SEO.
#: wp-seo-main.php:564
msgid "%s activation failed"
msgstr "Attivazione di %s fallita"

#. translators: %s: Yoast SEO.
#: src/presenters/admin/migration-error-presenter.php:42
msgid "%s is unable to create database tables"
msgstr "%s non è in grado di creare le tabelle di database"

#: src/presenters/admin/sidebar-presenter.php:81 js/dist/block-editor.js:27
#: js/dist/classic-editor.js:12 js/dist/elementor.js:12
#: js/dist/externals-components.js:199 js/dist/general-page.js:14
#: js/dist/integrations-page.js:48 js/dist/new-settings.js:14
#: js/dist/post-edit.js:12 js/dist/support.js:14 js/dist/term-edit.js:12
msgid "Buy now"
msgstr "Acquista ora"

#: src/presenters/admin/sidebar-presenter.php:70 js/dist/block-editor.js:26
#: js/dist/classic-editor.js:11 js/dist/elementor.js:11
#: js/dist/externals-components.js:198 js/dist/general-page.js:13
#: js/dist/integrations-page.js:47 js/dist/new-settings.js:13
#: js/dist/post-edit.js:11 js/dist/support.js:13 js/dist/term-edit.js:11
msgid "If you were thinking about upgrading, now's the time! 30% OFF ends 3rd Dec 11am (CET)"
msgstr "Se stavi pensando di fare un upgrade, questo è il momento giusto! Il 30% di sconto termina il 3 dicembre alle 11:00 (CET)."

#. translators: %1$s and %2$s expand to a span wrap to avoid linebreaks. %3$s
#. expands to "Yoast SEO Premium".
#: src/presenters/admin/sidebar-presenter.php:59 js/dist/block-editor.js:27
#: js/dist/classic-editor.js:12 js/dist/elementor.js:12
#: js/dist/externals-components.js:199 js/dist/general-page.js:14
#: js/dist/integrations-page.js:48 js/dist/new-settings.js:14
#: js/dist/post-edit.js:12 js/dist/support.js:14 js/dist/term-edit.js:12
msgid "%1$sBuy%2$s %3$s"
msgstr "%1$sCompra%2$s %3$s"

#: src/presenters/admin/sidebar-presenter.php:20 js/dist/block-editor.js:27
#: js/dist/block-editor.js:282 js/dist/classic-editor.js:12
#: js/dist/classic-editor.js:267 js/dist/editor-modules.js:173
#: js/dist/elementor.js:12 js/dist/elementor.js:64
#: js/dist/externals-components.js:186 js/dist/externals-components.js:199
#: js/dist/general-page.js:14 js/dist/integrations-page.js:48
#: js/dist/new-settings.js:14 js/dist/post-edit.js:12 js/dist/support.js:14
#: js/dist/term-edit.js:12
msgid "30% OFF - BLACK FRIDAY"
msgstr "30% DI SCONTO - BLACK FRIDAY"

#. translators: %s expands to Yoast SEO Premium
#: admin/class-premium-upsell-admin-block.php:100
msgid "Upgrade now"
msgstr "Aggiorna ora"

#: src/integrations/admin/check-required-version.php:115
msgid "Required Yoast SEO version"
msgstr "Versione di Yoast SEO richiesta"

#: src/integrations/admin/check-required-version.php:91
msgid "The package could not be installed because it's not supported by the currently installed Yoast SEO version."
msgstr "Il pacchetto non può essere installato perché non è supportato dalla versione di Yoast SEO attualmente installata."

#. translators: 1: Current Yoast SEO version, 2: Version required by the
#. uploaded plugin.
#: src/integrations/admin/check-required-version.php:84
msgid "The Yoast SEO version on your site is %1$s, however the uploaded plugin requires %2$s."
msgstr "La versione di Yoast SEO installata sul tuo sito è la %1$s, mentre il plugin che hai caricato richiede la %2$s."

#: src/user-meta/framework/custom-meta/noindex-author.php:108
msgid "Do not allow search engines to show this author's archives in search results."
msgstr "Non permettere ai motori di ricerca di mostrare gli archivi di questo autore nei risultati della ricerca."

#: admin/menu/class-base-menu.php:260 admin/menu/class-base-menu.php:264
msgid "Upgrades"
msgstr "Estensioni"

#: blocks/dynamic-blocks/breadcrumbs/block.json
msgctxt "block keyword"
msgid "site structure"
msgstr "struttura del sito"

#: blocks/dynamic-blocks/breadcrumbs/block.json
msgctxt "block keyword"
msgid "internal linking"
msgstr "link interno"

#: blocks/dynamic-blocks/breadcrumbs/block.json
msgctxt "block keyword"
msgid "breadcrumbs"
msgstr "breadcrumb"

#: blocks/dynamic-blocks/breadcrumbs/block.json
msgctxt "block description"
msgid "Adds the Yoast SEO breadcrumbs to your template or content."
msgstr "Aggiunge le breadcrumb di Yoast SEO al template o al contenuto."

#: blocks/dynamic-blocks/breadcrumbs/block.json
msgctxt "block title"
msgid "Yoast Breadcrumbs"
msgstr "Yoast Breadcrumbs"

#: blocks/structured-data-blocks/how-to/block.json
msgctxt "block keyword"
msgid "How to"
msgstr "Come fare per"

#: blocks/structured-data-blocks/how-to/block.json
msgctxt "block keyword"
msgid "How-to"
msgstr "Tutorial"

#: blocks/structured-data-blocks/how-to/block.json
msgctxt "block title"
msgid "Yoast How-to"
msgstr "Yoast How-to"

#: blocks/structured-data-blocks/faq/block.json
#: blocks/structured-data-blocks/how-to/block.json
msgctxt "block keyword"
msgid "Structured Data"
msgstr "Dati strutturati"

#: blocks/dynamic-blocks/breadcrumbs/block.json
#: blocks/structured-data-blocks/faq/block.json
#: blocks/structured-data-blocks/how-to/block.json
msgctxt "block keyword"
msgid "SEO"
msgstr "SEO"

#: blocks/structured-data-blocks/faq/block.json
#: blocks/structured-data-blocks/how-to/block.json
msgctxt "block keyword"
msgid "Schema"
msgstr "Schema"

#: blocks/structured-data-blocks/faq/block.json
msgctxt "block keyword"
msgid "Frequently Asked Questions"
msgstr "Domande frequenti"

#: blocks/structured-data-blocks/faq/block.json
msgctxt "block keyword"
msgid "FAQ"
msgstr "FAQ"

#: blocks/structured-data-blocks/faq/block.json
msgctxt "block description"
msgid "List your Frequently Asked Questions in an SEO-friendly way."
msgstr "Scrivi le tue domande frequenti (FAQ) in modo SEO-friendly."

#: blocks/structured-data-blocks/faq/block.json
msgctxt "block title"
msgid "Yoast FAQ"
msgstr "Blocco FAQ di Yoast"

#: admin/class-admin.php:315
#: src/user-meta/framework/additional-contactmethods/x.php:28
msgid "X username (without @)"
msgstr "Nome utente di X (senza @)"

#: src/presenters/admin/sidebar-presenter.php:94 js/dist/block-editor.js:27
#: js/dist/classic-editor.js:12 js/dist/elementor.js:12
#: js/dist/externals-components.js:199 js/dist/general-page.js:14
#: js/dist/integrations-page.js:48 js/dist/new-settings.js:14
#: js/dist/post-edit.js:12 js/dist/support.js:14 js/dist/term-edit.js:12
msgid "30-day money back guarantee."
msgstr "Garanzia di rimborso a 30 giorni."

#. translators: 1: PHP class name, 2: PHP variable name
#: inc/class-yoast-dynamic-rewrites.php:67
msgid "The %1$s class must not be instantiated before the %2$s global is set."
msgstr "La classe %1$s non deve essere istanziata prima che il globale %2$s sia impostato."

#. translators: %s expands to Yoast SEO Premium
#. translators: %s expands to "Yoast SEO" Premium
#: admin/class-premium-upsell-admin-block.php:100 js/dist/block-editor.js:35
#: js/dist/classic-editor.js:20 js/dist/elementor.js:20
#: js/dist/externals-components.js:201 js/dist/general-page.js:22
#: js/dist/integrations-page.js:56 js/dist/new-settings.js:22
#: js/dist/post-edit.js:20 js/dist/support.js:22 js/dist/term-edit.js:20
msgid "Explore %s now!"
msgstr "Esplora %s ora!"

#. translators: %1$s expands to a strong opening tag, %2$s expands to a strong
#. closing tag.
#: admin/class-premium-upsell-admin-block.php:88 js/dist/block-editor.js:33
#: js/dist/classic-editor.js:18 js/dist/editor-modules.js:180
#: js/dist/elementor.js:18 js/dist/externals-components.js:148
#: js/dist/general-page.js:21 js/dist/integrations-page.js:55
#: js/dist/new-settings.js:21 js/dist/post-edit.js:19 js/dist/support.js:21
#: js/dist/term-edit.js:19
msgid "%1$s24/7 support%2$s: Also on evenings and weekends."
msgstr "%1$sSupporto 24/7%2$s: anche la sera e nei weekend."

#. translators: %1$s expands to a strong opening tag, %2$s expands to a strong
#. closing tag.
#: admin/class-premium-upsell-admin-block.php:82 js/dist/block-editor.js:32
#: js/dist/classic-editor.js:17 js/dist/editor-modules.js:179
#: js/dist/elementor.js:17 js/dist/externals-components.js:147
#: js/dist/general-page.js:20 js/dist/integrations-page.js:54
#: js/dist/new-settings.js:20 js/dist/post-edit.js:18 js/dist/support.js:20
#: js/dist/term-edit.js:18
msgid "%1$sAppealing social previews%2$s people actually want to click on."
msgstr "%1$sAnteprime attraenti dei messaggi per i social%2$s che le persone vorranno cliccare."

#. translators: %1$s expands to a strong opening tag, %2$s expands to a strong
#. closing tag.
#: admin/class-premium-upsell-admin-block.php:76 js/dist/block-editor.js:31
#: js/dist/classic-editor.js:16 js/dist/editor-modules.js:178
#: js/dist/elementor.js:16 js/dist/externals-components.js:146
#: js/dist/general-page.js:19 js/dist/integrations-page.js:53
#: js/dist/new-settings.js:19 js/dist/post-edit.js:17 js/dist/support.js:19
#: js/dist/term-edit.js:17
msgid "%1$sNo more broken links%2$s: Automatic redirect manager."
msgstr "%1$sNiente più link rotti%2$s: gestione automatica dei reindirizzamenti."

#. translators: %1$s expands to a strong opening tag, %2$s expands to a strong
#. closing tag.
#: admin/class-premium-upsell-admin-block.php:70 js/dist/block-editor.js:30
#: js/dist/classic-editor.js:15 js/dist/editor-modules.js:177
#: js/dist/elementor.js:15 js/dist/externals-components.js:145
#: js/dist/general-page.js:18 js/dist/integrations-page.js:52
#: js/dist/new-settings.js:18 js/dist/post-edit.js:16 js/dist/support.js:18
#: js/dist/term-edit.js:16
msgid "%1$sSuper fast%2$s internal linking suggestions."
msgstr "Suggerimenti %1$srapidi%2$s di link interni."

#. translators: %1$s expands to a strong opening tag, %2$s expands to a strong
#. closing tag.
#: admin/class-premium-upsell-admin-block.php:64 js/dist/block-editor.js:29
#: js/dist/classic-editor.js:14 js/dist/editor-modules.js:176
#: js/dist/elementor.js:14 js/dist/externals-components.js:144
#: js/dist/general-page.js:17 js/dist/integrations-page.js:51
#: js/dist/new-settings.js:17 js/dist/post-edit.js:15 js/dist/support.js:17
#: js/dist/term-edit.js:15
msgid "%1$sMultiple keywords%2$s: Rank higher for more searches."
msgstr "%1$sFino a 5 parole chiave%2$s: posizionati più in alto per un maggior numero di ricerche."

#. translators: %1$s expands to a strong opening tag, %2$s expands to a strong
#. closing tag.
#: admin/class-premium-upsell-admin-block.php:58 js/dist/block-editor.js:28
#: js/dist/classic-editor.js:13 js/dist/editor-modules.js:175
#: js/dist/elementor.js:13 js/dist/externals-components.js:143
#: js/dist/general-page.js:16 js/dist/integrations-page.js:50
#: js/dist/new-settings.js:16 js/dist/post-edit.js:14 js/dist/support.js:16
#: js/dist/term-edit.js:14
msgid "%1$sAI%2$s: Better SEO titles and meta descriptions, faster."
msgstr "%1$sIA%2$s: titoli e meta-descrizioni SEO migliori, e in modo più veloce."

#: admin/watchers/class-slug-change-watcher.php:68
msgid "Search engines and other websites can still send traffic to your trashed content."
msgstr "I motori di ricerca e altri siti web possono ancora inviare traffico ai tuoi contenuti cestinati."

#. translators: 1: Yoast SEO, 2: Link start tag to the Learn more link, 3: Link
#. closing tag.
#: src/presenters/admin/woocommerce-beta-editor-presenter.php:53
msgid "The %1$s interface is currently unavailable in the beta WooCommerce product editor. To resolve any issues, please disable the beta editor. %2$sLearn how to disable the beta WooCommerce product editor.%3$s"
msgstr "L'interfaccia di %1$s non è al momento disponibile nell'editor beta dei prodotti WooCommerce. Per risolvere eventuali problemi, disattiva l'editor beta. %2$sImpara a disabilitare l'editor beta di prodotti WooCommerce.%3$s"

#: src/presenters/admin/woocommerce-beta-editor-presenter.php:50
msgid "Compatibility issue: Yoast SEO is incompatible with the beta WooCommerce product editor."
msgstr "Problema di compatibilità: Yoast SEO è al momento incompatibile con l'editor di prodotti beta di WooCommerce."

#: src/presenters/admin/sidebar-presenter.php:73 js/dist/block-editor.js:25
#: js/dist/classic-editor.js:10 js/dist/elementor.js:10
#: js/dist/externals-components.js:197 js/dist/general-page.js:12
#: js/dist/integrations-page.js:46 js/dist/new-settings.js:12
#: js/dist/post-edit.js:10 js/dist/support.js:12 js/dist/term-edit.js:10
msgid "Use AI to generate titles and meta descriptions, automatically redirect deleted pages, get 24/7 support, and much, much more!"
msgstr "Usa l'intelligenza artificiale (IA) per generare titoli e meta descrizioni, reindirizzare automaticamente le pagine cancellate, ottenere assistenza 24/7 e molto altro ancora!"

#. translators: %1$s is a <br> tag.
#: inc/class-addon-manager.php:413
msgid "%1$s Now with 30%% Black Friday Discount!"
msgstr "%1$s Ora con un 30%% di sconto Black Friday!"

#: admin/class-premium-upsell-admin-block.php:116
#: admin/menu/class-base-menu.php:264 inc/class-wpseo-admin-bar-menu.php:597
#: js/dist/block-editor.js:33 js/dist/classic-editor.js:18
#: js/dist/elementor.js:18 js/dist/externals-components.js:199
#: js/dist/general-page.js:14 js/dist/integrations-page.js:48
#: js/dist/new-settings.js:14 js/dist/post-edit.js:12 js/dist/support.js:14
#: js/dist/term-edit.js:12
msgid "30% OFF"
msgstr "30% di sconto"

#: admin/class-premium-upsell-admin-block.php:115 js/dist/block-editor.js:33
#: js/dist/classic-editor.js:18 js/dist/elementor.js:18
#: js/dist/externals-components.js:199 js/dist/general-page.js:14
#: js/dist/integrations-page.js:48 js/dist/new-settings.js:14
#: js/dist/post-edit.js:12 js/dist/support.js:14 js/dist/term-edit.js:12
msgid "BLACK FRIDAY"
msgstr "BLACK FRIDAY"

#: admin/views/class-yoast-feature-toggles.php:209
msgid "Use the power of Yoast AI to automatically generate compelling titles and descriptions for your posts and pages."
msgstr "Sfrutta la potenza di Yoast AI per generare automaticamente titoli e descrizioni convincenti per i tuoi post e le tue pagine."

#: admin/views/class-yoast-feature-toggles.php:206 js/dist/new-settings.js:38
#: js/dist/new-settings.js:312
msgid "AI title & description generator"
msgstr "Generatore IA di titoli e descrizioni"

#. translators: %s expands to a unit of time (e.g. 1 day).
#. translators: %1$s, %2$s and %3$s expand to units of time (e.g. 1 day).
#: src/integrations/blocks/structured-data-blocks.php:178
#: js/dist/how-to-block.js:10 js/dist/how-to-block.js:16
msgid "%1$s, %2$s and %3$s"
msgstr "%1$s, %2$s e %3$s"

#. translators: %s expands to a unit of time (e.g. 1 day).
#. translators: %1$s and %2$s expand to units of time (e.g. 1 day).
#: src/integrations/blocks/structured-data-blocks.php:172
#: js/dist/how-to-block.js:9 js/dist/how-to-block.js:15
msgid "%1$s and %2$s"
msgstr "%1$s e %2$s"

#. translators: 1: Opening tag of the link to the Search appearance settings
#. page, 2: Link closing tag.
#: src/content-type-visibility/application/content-type-visibility-watcher-actions.php:157
msgid "You've added a new type of content. We recommend that you review the corresponding %1$sSearch appearance settings%2$s."
msgstr "Hai aggiunto un nuovo tipo di contenuto. Rivedi le %1$simpostazioni dell'Aspetto della ricerca%2$s."

#: src/content-type-visibility/application/content-type-visibility-dismiss-notifications.php:73
msgid "Error: Taxonomy was not removed from new_taxonomies list."
msgstr "Errore: la tassonomia non è stata rimossa dall'elenco new_taxonomies list."

#: src/content-type-visibility/application/content-type-visibility-dismiss-notifications.php:73
msgid "Taxonomy is no longer new."
msgstr "La tassonomia non è più nuova."

#: src/content-type-visibility/application/content-type-visibility-dismiss-notifications.php:66
msgid "Taxonomy is not new."
msgstr "La tassonomia non è nuova."

#: src/content-type-visibility/application/content-type-visibility-dismiss-notifications.php:43
msgid "Error: Post type was not removed from new_post_types list."
msgstr "Errore: il tipo di contenuto non è stato rimosso dall'elenco new_post_types."

#: src/content-type-visibility/application/content-type-visibility-dismiss-notifications.php:43
msgid "Post type is no longer new."
msgstr "Il tipo di contenuto (post type) non è più nuovo."

#: src/content-type-visibility/application/content-type-visibility-dismiss-notifications.php:37
msgid "Post type is not new."
msgstr "Il tipo di contenuto (post type) non è nuovo."

#: src/integrations/support-integration.php:108 js/dist/support.js:24
msgid "Support"
msgstr "Supporto"

#: src/integrations/admin/crawl-settings-integration.php:163
#: js/dist/new-settings.js:38 js/dist/new-settings.js:181
msgid "Prevent Google AdsBot from crawling"
msgstr "Impedisci il crawling di Google AdsBot"

#: src/integrations/admin/background-indexing-integration.php:186
msgid "Every fifteen minutes"
msgstr "Ogni quindici minuti"

#: src/commands/index-command.php:174
msgid "Your WordPress environment is running on a non-production site. Indexables can only be created on production environments. Please check your `WP_ENVIRONMENT_TYPE` settings."
msgstr "L'ambiente WordPress è in esecuzione su un sito non di produzione. Gli indexables possono essere creati solo in ambienti di produzione. Controlla le tue impostazioni `WP_ENVIRONMENT_TYPE`."

#. translators: %s expands to the inclusive language score
#: inc/class-wpseo-rank.php:239 inc/class-wpseo-rank.php:244
#: inc/class-wpseo-rank.php:249
msgid "Inclusive language: %s"
msgstr "Linguaggio inclusivo: %s"

#. translators: %1$s expands to Yoast SEO, %2$s to Wincher
#: admin/class-wincher-dashboard-widget.php:58
msgid "%1$s / %2$s: Top Keyphrases"
msgstr "%1$s / %2$s: Migliori frasi chiave"

#. translators: %s: expands to the post type
#: src/exceptions/indexable/post-type-not-built-exception.php:20
msgid "The post type %s could not be indexed because it does not meet indexing requirements."
msgstr "Il tipo di contenuto %s non può essere indicizzato perché non soddisfa i requisiti di indicizzazione."

#: src/integrations/academy-integration.php:111 js/dist/academy.js:2
msgid "Academy"
msgstr "Academy"

#. translators: %1$s expands to a strong tag, %2$s expands to the product name,
#. %3$s expands to a closing strong tag, %4$s expands to an a tag. %5$s expands
#. to MyYoast, %6$s expands to a closing a tag,  %7$s expands to the product
#. name
#: inc/class-addon-manager.php:522
msgid "%1$s %2$s isn't working as expected %3$s and you are not receiving updates or support! Make sure to %4$s activate your product subscription in %5$s%6$s to unlock all the features of %7$s."
msgstr "%1$s %2$s non sta funzionando come previsto %3$s e non stai ricevendo nè aggiornamenti nè supporto! Assicurati %4$s che l'abbonamento sia attivato nel tuo account %5$s%6$s per sbloccare tutte le funzionalità di %7$s."

#. translators: %1$s is the plugin name, %2$s and %3$s are a link.
#: inc/class-addon-manager.php:421
msgid "%1$s can't be updated because your product subscription is expired. %2$sRenew your product subscription%3$s to get updates again and use all the features of %1$s."
msgstr "Non è stato possibile aggiornare %1$s perché il tuo abbonamento è scaduto. %2$sRinnova il tuo abbonamento%3$s per ottenere gli aggiornamenti e usare tutte le funzionalità di %1$s."

#: src/integrations/admin/crawl-settings-integration.php:292
msgid "This feature is disabled when your site is not using pretty permalinks."
msgstr "Questa funzione è disattivata quando il sito non usa i pretty permalink."

#. translators: 1: Link start tag to the Permalinks settings page, 2: Link
#. closing tag.
#: src/integrations/admin/crawl-settings-integration.php:286
msgid "This feature is disabled when your site is not using %1$spretty permalinks%2$s."
msgstr "Questa funzione è disattivata quando il sito non usa i %1$spretty permalink%2$s."

#. translators: %1$s: Yoast SEO
#: src/helpers/crawl-cleanup-helper.php:271
msgid "%1$s: unregistered URL parameter removed. See %2$s"
msgstr "%1$s: il parametro URL non registrato è stato rimosso. Vedi %2$s"

#. translators: %1$s: Yoast SEO
#: src/initializers/crawl-cleanup-permalinks.php:144
msgid "%1$s: redirect utm variables to #"
msgstr "%1$s: reindirizza le variabili utm a #"

#: src/presenters/admin/indexing-notification-presenter.php:93
msgid "It looks like you've enabled media pages. We recommend that you help us to re-analyze your site by running the SEO data optimization."
msgstr "Sembra che tu abbia attivato le pagine media. Ti chiediamo di aiutarci a rianalizzare il tuo sito eseguendo l'ottimizzazione dei dati SEO."

#: inc/class-wpseo-rank.php:156 inc/class-wpseo-rank.php:245
#: js/dist/block-editor.js:540 js/dist/editor-modules.js:315
#: js/dist/elementor.js:94 js/dist/externals-components.js:188
#: js/dist/externals-components.js:282
#: js/dist/frontend-inspector-resources.js:1 js/dist/post-edit.js:25
#: js/dist/term-edit.js:1
msgid "Potentially non-inclusive"
msgstr "Potenzialmente non inclusivo"

#. translators: CTA to finish the first time configuration. %s: Either
#. first-time SEO configuration or SEO configuration.
#: admin/class-admin.php:235
msgid "Finish your %s"
msgstr "Termina la %s"

#. translators: %1$s and %2$s expand to a span wrap to avoid linebreaks. %3$s
#. expands to "Yoast SEO Premium".
#: src/presenters/admin/sidebar-presenter.php:63 js/dist/block-editor.js:26
#: js/dist/classic-editor.js:11 js/dist/elementor.js:11
#: js/dist/externals-components.js:198 js/dist/general-page.js:13
#: js/dist/integrations-page.js:47 js/dist/new-settings.js:13
#: js/dist/post-edit.js:11 js/dist/support.js:13 js/dist/term-edit.js:11
msgid "%1$sGet%2$s %3$s"
msgstr "%1$sOttieni%2$s %3$s"

#: inc/class-wpseo-admin-bar-menu.php:572
msgid "WordPress.org support forums"
msgstr "Forum di assistenza di WordPress.org"

#: inc/class-wpseo-admin-bar-menu.php:567
msgid "Yoast Premium support"
msgstr "Assistenza di Yoast Premium"

#: inc/class-wpseo-admin-bar-menu.php:562
msgid "Yoast.com help section"
msgstr "Sezione assistenza di Yoast.com"

#: inc/class-wpseo-admin-bar-menu.php:547
msgid "Help"
msgstr "Aiuto"

#: inc/class-wpseo-admin-bar-menu.php:528
msgid "Write better content"
msgstr "Scrivi contenuti migliori"

#: inc/class-wpseo-admin-bar-menu.php:523
msgid "Improve your blog post"
msgstr "Migliora il tuo articolo del blog"

#: inc/class-wpseo-admin-bar-menu.php:518
#: inc/class-wpseo-admin-bar-menu.php:577
msgid "Learn more SEO"
msgstr "Impara la SEO"

#: inc/class-wpseo-admin-bar-menu.php:473
msgid "SEO Tools"
msgstr "Strumenti SEO"

#: inc/class-wpseo-admin-bar-menu.php:236
msgid "Focus keyphrase: "
msgstr "Frase chiave: "

#: inc/class-wpseo-admin-bar-menu.php:230
msgid "not set"
msgstr "non impostato"

#: src/presenters/admin/indexing-notification-presenter.php:90
msgid "We need to re-analyze some of your SEO data because of a change in the visibility of your taxonomies. Please help us do that by running the SEO data optimization."
msgstr "Abbiamo bisogno di analizzare nuovamente alcuni dati SEO a causa di una modifica della visibilità delle tassonomie. Aiutaci a farlo eseguendo l'ottimizzazione dei dati SEO."

#: src/presenters/admin/indexing-notification-presenter.php:87
msgid "We need to re-analyze some of your SEO data because of a change in the visibility of your post types. Please help us do that by running the SEO data optimization."
msgstr "Abbiamo bisogno di analizzare nuovamente alcuni dati SEO a causa di una modifica della visibilità dei tipi di contenuto. Aiutaci a farlo eseguendo l'ottimizzazione dei dati SEO."

#. translators: %s: expands to the term id
#: src/exceptions/indexable/term-not-built-exception.php:20
msgid "The term %s could not be built because it's not indexable."
msgstr "Il termine %s non può essere costruito perché non è indicizzabile."

#. translators: %s: expands to the post id
#: src/exceptions/indexable/post-not-built-exception.php:32
msgid "The post %s could not be indexed because it's post type is excluded from indexing."
msgstr "L'articolo %s non può essere indicizzato perché questo tipo di post è escluso dall'indicizzazione."

#. translators: %s: expands to the post id
#: src/exceptions/indexable/post-not-built-exception.php:20
msgid "The post %s could not be indexed because it does not meet indexing requirements."
msgstr "L'articolo %s non può essere indicizzato, perché non soddisfa i requisiti di indicizzazione."

#. translators: %1$d is the number of records that were removed. %2$s is the
#. site url.
#: src/commands/cleanup-command.php:183
msgid "Cleaned up %1$d record from %2$s."
msgid_plural "Cleaned up %1$d records from %2$s."
msgstr[0] "%1$d record da %2$s ripulito."
msgstr[1] "%1$d record da %2$s ripuliti."

#. translators: %1$s is the site url of the site that is cleaned up. %2$s is
#. the name of the cleanup task that is currently running.
#: src/commands/cleanup-command.php:159
msgid "Cleaning up %1$s [%2$s]"
msgstr "Pulizia di %1$s [%2$s]"

#. translators: %1$s is the site url of the site that is skipped. %2$s is Yoast
#. SEO.
#: src/commands/cleanup-command.php:146
msgid "Skipping %1$s. %2$s is not active on this site."
msgstr "Ignora %1$s. %2$s non è attivo su questo sito."

#. translators: %1$d is the number of records that are removed.
#: src/commands/cleanup-command.php:97
msgid "Cleaned up %1$d record."
msgid_plural "Cleaned up %1$d records."
msgstr[0] "%1$d record ripulito."
msgstr[1] "%1$d record ripuliti."

#: src/commands/cleanup-command.php:84
msgid "The value for 'batch-size' must be a positive integer higher than equal to 1."
msgstr "Il valore di \"batch-size\" deve essere un numero intero positivo maggiore di 1."

#: src/commands/cleanup-command.php:81
msgid "The value for 'interval' must be a positive integer."
msgstr "Il valore di \"intervallo\" deve essere un numero intero positivo."

#: admin/views/class-yoast-feature-toggles.php:210 js/dist/general-page.js:23
#: js/dist/general-page.js:35 js/dist/integrations-page.js:3
#: js/dist/integrations-page.js:6 js/dist/integrations-page.js:18
#: js/dist/integrations-page.js:57 js/dist/new-settings.js:312
#: js/dist/plans.js:2 js/dist/post-edit.js:28
msgid "Learn more"
msgstr "Ulteriori informazioni"

#: src/integrations/admin/crawl-settings-integration.php:157
msgid "Redirect pretty URLs for search pages to raw format"
msgstr "Reindirizza i pretty URL delle pagine di ricerca al formato grezzo"

#. Translators: %1$s expands to an opening anchor tag for a link leading to the
#. Yoast SEO page of the Permalink Cleanup features, %2$s expands to a closing
#. anchor tag.
#: src/integrations/admin/crawl-settings-integration.php:204
msgid "These are expert features, so make sure you know what you're doing before removing the parameters. %1$sRead more about how your site can be affected%2$s."
msgstr "Queste sono funzioni avanzate, è importante che tu sappia cosa stai facendo prima di rimuovere i parametri. %1$sInformati su quali potrebbero essere gli effetti sul tuo sito%2$s."

#: src/presenters/admin/sidebar-presenter.php:101 js/dist/block-editor.js:27
#: js/dist/classic-editor.js:12 js/dist/elementor.js:12
#: js/dist/externals-components.js:199 js/dist/general-page.js:14
#: js/dist/integrations-page.js:48 js/dist/new-settings.js:14
#: js/dist/post-edit.js:12 js/dist/support.js:14 js/dist/term-edit.js:12
msgid "Read reviews from real users"
msgstr "Leggi le recensioni di utenti reali"

#. translators: %1$s expands to Yoast SEO, %2$s expands to the name of the
#. class that could not be found.
#: src/loader.php:258
msgid "%1$s attempted to load the class %2$s but it could not be found."
msgstr "%1$s ha cercato di caricare la classe %2$s ma non l'ha trovata."

#: src/integrations/admin/crawl-settings-integration.php:189
#: js/dist/new-settings.js:181
msgid "Remove unused resources"
msgstr "Rimuove le risorse inutilizzate"

#: src/integrations/admin/crawl-settings-integration.php:162
msgid "Prevent search engines from crawling /wp-json/"
msgstr "Impedisce ai motori di ricerca di scansionare /wp-json/"

#: src/integrations/admin/crawl-settings-integration.php:156
msgid "Prevent search engines from crawling site search URLs"
msgstr "Impedisce ai motori di ricerca di scansionare gli URL provenienti dalle ricerche sul sito"

#: admin/views/user-profile.php:74
#: src/user-meta/framework/custom-meta/inclusive-language-analysis-disable.php:115
msgid "Removes the inclusive language analysis section from the metabox and disables all inclusive language-related suggestions."
msgstr "Rimuove la sezione di analisi del linguaggio inclusivo dal metabox e disabilita tutti i suggerimenti relativi al linguaggio inclusivo."

#: admin/views/user-profile.php:71
#: src/user-meta/framework/custom-meta/inclusive-language-analysis-disable.php:109
msgid "Disable inclusive language analysis"
msgstr "Disabilita l'analisi del linguaggio inclusivo"

#: admin/views/class-yoast-feature-toggles.php:95
msgid "Discover why inclusive language is important for SEO."
msgstr "Scopri perché il linguaggio inclusivo è importante per la SEO."

#: admin/views/class-yoast-feature-toggles.php:94
msgid "The inclusive language analysis offers suggestions to write more inclusive copy."
msgstr "L'analisi del linguaggio inclusivo offre suggerimenti per scrivere un contenuto più inclusivo. "

#: admin/views/class-yoast-feature-toggles.php:91 js/dist/new-settings.js:38
#: js/dist/new-settings.js:312
msgid "Inclusive language analysis"
msgstr "Analisi del linguaggio inclusivo"

#: admin/metabox/class-metabox-section-inclusive-language.php:30
#: js/dist/externals-components.js:284
msgid "Inclusive language"
msgstr "Linguaggio inclusivo"

#: inc/class-wpseo-admin-bar-menu.php:269
msgid "Front-end SEO inspector"
msgstr "Tool di ispezione SEO nel front-end"

#: admin/class-yoast-form.php:933
msgid "Unlock with Premium!"
msgstr "Sblocca con la versione Premium!"

#. translators: 1: Yoast SEO Premium
#: src/integrations/admin/deactivated-premium-integration.php:99
msgid "Activate %1$s!"
msgstr "Attiva %1$s!"

#. translators: 1: Yoast SEO Premium 2: Link start tag to activate premium, 3:
#. Link closing tag.
#: src/integrations/admin/deactivated-premium-integration.php:86
msgid "You've installed %1$s but it's not activated yet. %2$sActivate %1$s now!%3$s"
msgstr "Hai installato %1$s ma non è ancora attivato. %2$sAttiva %1$s ora!%3$s"

#: src/integrations/admin/crawl-settings-integration.php:212
msgid "Permalink cleanup settings"
msgstr "Impostazioni di pulizia dei permalink"

#: src/integrations/admin/crawl-settings-integration.php:198
msgid "Search cleanup settings"
msgstr "Impostazioni di pulizia della ricerca"

#: src/integrations/admin/crawl-settings-integration.php:155
#: js/dist/new-settings.js:38 js/dist/new-settings.js:182
msgid "Filter searches with common spam patterns"
msgstr "Filtra le ricerche con modelli di spam comuni"

#: src/integrations/admin/crawl-settings-integration.php:154
#: js/dist/new-settings.js:38 js/dist/new-settings.js:182
msgid "Filter searches with emojis and other special characters"
msgstr "Filtra le ricerche con emoji e altri caratteri speciali"

#: src/integrations/admin/crawl-settings-integration.php:153
#: js/dist/new-settings.js:38 js/dist/new-settings.js:182
msgid "Filter search terms"
msgstr "Filtra i termini di ricerca"

#: src/integrations/admin/crawl-settings-integration.php:149
msgid "Unregistered URL parameters"
msgstr "Parametri URL non registrati"

#: src/integrations/admin/crawl-settings-integration.php:148
msgid "Campaign tracking URL parameters"
msgstr "Parametri URL di monitoraggio della campagna"

#: src/deprecated/src/config/wordproof-translations.php:129
msgid "Contact WordProof support"
msgstr "Contatta il supporto di WordProof"

#: admin/views/class-yoast-feature-toggles.php:199
msgid "Find out how IndexNow can help your site."
msgstr "Scopri come IndexNow può aiutare il tuo sito."

#: admin/views/class-yoast-feature-toggles.php:198 js/dist/new-settings.js:316
msgid "Automatically ping search engines like Bing and Yandex whenever you publish, update or delete a post."
msgstr "Esegui automaticamente il ping ai motori di ricerca come Bing e Yandex ogni volta che pubblichi, aggiorni o elimini un articolo."

#: admin/views/class-yoast-feature-toggles.php:195 js/dist/new-settings.js:38
#: js/dist/new-settings.js:316
msgid "IndexNow"
msgstr "IndexNow"

#. translators: 1: Link start tag to the first-time configuration, 2: Link
#. closing tag.
#: src/integrations/admin/first-time-configuration-notice-integration.php:119
msgid "We noticed that you haven't fully configured Yoast SEO yet. Optimize your SEO settings even further by using our improved %1$s First-time configuration%2$s."
msgstr "Abbiamo notato che non hai ancora completato la configurazione di Yoast SEO. Ottimizza tutte le tue impostazioni SEO con la nostra procedura di %1$sprima configurazione%2$s."

#: src/helpers/first-time-configuration-notice-helper.php:64
msgid "SEO configuration"
msgstr "Configurazione SEO"

#: src/integrations/admin/crawl-settings-integration.php:188
msgid "Feed crawl settings"
msgstr "Impostazioni di crawl del feed"

#: src/integrations/admin/crawl-settings-integration.php:186
msgid "Basic crawl settings"
msgstr "Impostazioni base di crawl"

#: src/integrations/admin/crawl-settings-integration.php:144
msgid "Powered by HTTP header"
msgstr "Creato da header HTTP"

#: src/integrations/admin/crawl-settings-integration.php:143
#: js/dist/new-settings.js:38 js/dist/new-settings.js:181
msgid "Pingback HTTP header"
msgstr "Pingback degli header HTTP"

#: src/integrations/admin/crawl-settings-integration.php:161
msgid "Emoji scripts"
msgstr "Script emoji"

#: src/integrations/admin/crawl-settings-integration.php:142
msgid "Generator tag"
msgstr "Tag generatore"

#: src/integrations/admin/crawl-settings-integration.php:141
msgid "oEmbed links"
msgstr "Link oEmbed"

#: src/integrations/admin/crawl-settings-integration.php:140
msgid "RSD / WLW links"
msgstr "Link RSD / WLW"

#: src/integrations/admin/crawl-settings-integration.php:139
msgid "REST API links"
msgstr "Link REST API"

#: src/integrations/admin/crawl-settings-integration.php:138
msgid "Shortlinks"
msgstr "Shortlink"

#: src/integrations/admin/crawl-settings-integration.php:134
msgid "Atom/RDF feeds"
msgstr "Feed Atom/RDF"

#: src/integrations/admin/crawl-settings-integration.php:133
msgid "Search results feeds"
msgstr "Feed dei risultati di ricerca"

#: src/integrations/admin/crawl-settings-integration.php:132
msgid "Custom taxonomy feeds"
msgstr "Feed di tassonomia personalizzati"

#: src/integrations/admin/crawl-settings-integration.php:131
msgid "Tag feeds"
msgstr "Feed dei tag"

#: src/integrations/admin/crawl-settings-integration.php:130
msgid "Category feeds"
msgstr "Feed di categoria"

#: src/integrations/admin/crawl-settings-integration.php:129
msgid "Post type feeds"
msgstr "Feed dei post type"

#: src/integrations/admin/crawl-settings-integration.php:128
msgid "Post authors feeds"
msgstr "Feed autori degli articoli"

#: src/integrations/admin/crawl-settings-integration.php:126
msgid "Global comment feeds"
msgstr "Feed dei commenti globali"

#: src/integrations/admin/crawl-settings-integration.php:125
msgid "Global feed"
msgstr "Feed globale"

#: src/integrations/admin/crawl-settings-integration.php:127
msgid "Post comments feeds"
msgstr "Feed dei commenti agli articoli"

#. translators: %1$s opens the link to the Yoast.com article about Crawl
#. settings, %2$s closes the link,
#: admin/views/tabs/network/crawl-settings.php:31
msgid "%1$sLearn more about crawl settings.%2$s"
msgstr "%1$sMaggiori informazioni sulle impostazioni di crawl.%2$s"

#: admin/views/redirects.php:188
msgid "No items found."
msgstr "Nessun elemento trovato"

#: admin/views/redirects.php:137
msgid "All redirect types"
msgstr "Tutti i tipi di redirezione"

#: admin/views/redirects.php:122
msgid "Add Redirect"
msgstr "Aggiungi redirect"

#: admin/views/redirects.php:100 admin/views/redirects.php:165
#: admin/views/redirects.php:212
msgid "Old URL"
msgstr "Vecchio URL"

#. translators: 1: opens a link. 2: closes the link.
#: admin/views/redirects.php:91
msgid "The redirect type is the HTTP response code sent to the browser telling the browser what type of redirect is served. %1$sLearn more about redirect types%2$s."
msgstr "Il tipo di redirect è il codice di risposta HTTP inviato al browser, che dice al browser  quale tipo di redirect viene fornito. %1$sImpara di più sui tipi di redirect%2$s."

#: admin/views/redirects.php:66 admin/views/redirects.php:75
msgid "301 Moved Permanently"
msgstr "301 Spostato Permanentemente"

#: admin/views/redirects.php:59 admin/views/redirects.php:155
#: admin/views/redirects.php:203
msgid "Type"
msgstr "Tipo"

#: admin/views/redirects.php:50
msgid "Plain redirects"
msgstr "Reindirizza"

#: admin/views/redirects.php:37
msgid "Regex Redirects"
msgstr "Regex redirect"

#: admin/pages/network.php:25 admin/views/tabs/network/crawl-settings.php:19
msgid "Crawl settings"
msgstr "Impostazioni di crawl"

#: src/integrations/admin/first-time-configuration-integration.php:128
#: js/dist/general-page.js:55
msgid "First-time configuration"
msgstr "Configurazione iniziale"

#. translators: 1: Link start tag to the First time configuration tab in the
#. General page, 2: Link closing tag.
#: admin/views/tabs/tool/import-seo.php:106
msgid "You should finish the %1$sfirst time configuration%2$s to make sure your SEO data has been optimized and you’ve set the essential Yoast SEO settings for your site."
msgstr "Dovresti completare la %1$s configurazione iniziale%2$s per assicurarti che i tuoi dati SEO siano stati ottimizzati e che tu abbia salvato le impostazioni essenziali di Yoast SEO per il tuo sito."

#: admin/views/tabs/tool/import-seo.php:100
msgid "Step 4: Go through the first time configuration"
msgstr "Passaggio 4: imposta la configurazione iniziale"

#: src/integrations/admin/import-integration.php:220
msgid "If you already have saved AIOSEO 'Search Appearance' settings and the issue persists, please contact our support team so we can take a closer look."
msgstr "Se hai già salvato le impostazioni per l'\"Aspetto di ricerca\" AIOSEO e il problema persiste, contatta il nostro team di assistenza così possiamo dare un'occhiata."

#: src/integrations/admin/import-integration.php:217
msgid "If you have never saved any AIOSEO 'Search Appearance' settings, please do that first and run the import again."
msgstr "Se non hai mai salvato nessuna impostazione per l'\"Aspetto di ricerca\" AIOSEO, fai prima quello e prova a ripetere l'importazione."

#: src/integrations/admin/import-integration.php:214
msgid "The AIOSEO import was cancelled because some AIOSEO data is missing. Please try and take the following steps to fix this:"
msgstr "L'importazione dei dati AIOSEO è stata annullata perché mancano alcuni dati. Prova a seguire i prossimi passaggi per risolvere il problema:"

#: src/exceptions/importing/aioseo-validation-exception.php:17
msgid "The validation of the AIOSEO data structure has failed."
msgstr "La convalida della struttura dati AIOSEO non è andata a buon fine."

#: src/deprecated/src/integrations/third-party/wordproof-integration-toggle.php:122
msgid "The WordProof Timestamp plugin needs to be disabled before you can activate this integration."
msgstr "Il plugin WordProof Timestamp deve essere disattivato prima di poter attivare questa integrazione."

#. translators: %s expands to WordProof
#: src/deprecated/src/integrations/third-party/wordproof-integration-toggle.php:116
msgid "Currently, the %s integration is not available for multisites."
msgstr "Al momento l'integrazione %s non è disponibile per i multisiti."

#: src/deprecated/src/config/wordproof-translations.php:115
msgid "Open settings"
msgstr "Impostazioni di apertura"

#: src/deprecated/src/config/wordproof-translations.php:101
msgid "Open authentication"
msgstr "Autenticazione aperta"

#. translators: %s expands to WordProof.
#: src/deprecated/src/config/wordproof-translations.php:87
msgid "The timestamp is not created because you need to authenticate with %s first."
msgstr "Il timestamp non viene creato perché è necessario autenticarti prima con %s."

#. translators: %s expands to WordProof.
#: src/deprecated/src/config/wordproof-translations.php:72
msgid "The timestamp is not retrieved by your site. Please try again or contact %1$s support."
msgstr "Il timestamp non viene recuperato dal sito. Riprova o contatta l'assistenza %1$s"

#. translators: %s expands to WordProof.
#: src/deprecated/src/config/wordproof-translations.php:57
msgid "%1$s failed to timestamp this page. Please check if you're correctly authenticated with %1$s and try to save this page again."
msgstr "%1$s non è riuscito a salvare la pagina con il timestamp. Verifica la corretta autenticazione con %1$s e riprova a salvare la pagina."

#. translators: %s expands to WordProof.
#: src/deprecated/src/config/wordproof-translations.php:42
msgid "%s has successfully timestamped this page."
msgstr "%s ha eseguito il timestamp di questa pagina."

#. translators: %s expands to WordProof.
#: src/deprecated/src/config/wordproof-translations.php:27
msgid "You are out of timestamps. Please upgrade your account by opening the %s settings."
msgstr "Non ci sono più timestamp. Aggiorna il tuo account aprendo le impostazioni %s."

#: src/integrations/admin/import-integration.php:236
msgid "Cleanup failed with the following error:"
msgstr "Procedura di cancellazione fallita con il seguente errore:"

#: src/integrations/admin/import-integration.php:120
msgid "Note: These settings will overwrite the default settings of Yoast SEO."
msgstr "Nota: queste impostazioni rimpiazzeranno le impostazioni predefinite di Yoast SEO."

#: src/integrations/admin/import-integration.php:116
#: src/integrations/admin/import-integration.php:126
msgid "Note: This metadata will only be imported if there is no existing Yoast SEO metadata yet."
msgstr "Nota: Questi metadati verranno importati solo se non esistono già metadati di Yoast SEO."

#: src/integrations/admin/import-integration.php:115
#: src/integrations/admin/import-integration.php:125
msgid "Post metadata (SEO titles, descriptions, etc.)"
msgstr "Metadati dell'articolo (titolo SEO, descrizione, etc.)"

#. translators: %s: expands to the name of the plugin that is selected to be
#. imported
#: src/integrations/admin/import-integration.php:111
msgid "The import from %s includes:"
msgstr "L'importazione da %s include:"

#: src/integrations/admin/import-integration.php:109
msgid "Once you're certain that your site is working properly with the imported data from another SEO plugin, you can clean up all the original data from that plugin."
msgstr "Quando sei sicuro che il tuo sito funzioni bene con i dati importati da un altro plugin SEO, puoi rimuovere tutti i dati usati originariamente da quel plugin."

#: src/integrations/admin/import-integration.php:108
msgid "Please select an SEO plugin below to see what data can be imported."
msgstr "Seleziona un plugin SEO per vedere quali dati possono essere importati."

#: admin/views/tool-import-export.php:35
#: src/integrations/admin/import-integration.php:107
msgid "Clean up"
msgstr "Cancella"

#: src/integrations/admin/import-integration.php:99
msgid "After you've imported data from another SEO plugin, please make sure to clean up all the original data from that plugin. (step 5)"
msgstr "Dopo aver importato i dati da un altro plugin SEO, assicurati di aver rimosso i dati utilizzati originariamente da quel plugin. "

#: src/integrations/admin/import-integration.php:98
msgid "Note: "
msgstr "Nota:"

#: src/integrations/admin/import-integration.php:97
msgid "The cleanup can take a long time depending on your site's size."
msgstr "A seconda della dimensione del tuo sito, la pulizia può richiedere un po' di tempo."

#: src/integrations/admin/import-integration.php:238
msgid "Import failed with the following error:"
msgstr "L'importazione è fallita con il seguente errore:"

#: src/integrations/admin/import-integration.php:101
msgid "No data found from other SEO plugins."
msgstr "Non è stato trovato alcun dato dagli altri plugin SEO."

#: src/integrations/admin/import-integration.php:100
msgid "Select SEO plugin"
msgstr "Seleziona il plugin SEO"

#: src/integrations/admin/import-integration.php:96
msgid "The import can take a long time depending on your site's size."
msgstr "L'importazione può richiedere molto tempo a seconda delle dimensioni del tuo sito"

#: src/integrations/admin/installation-success-integration.php:104
msgid "Installation Successful"
msgstr "Installazione riuscita"

#: src/config/schema-types.php:131
msgid "Blog Post"
msgstr "Articolo del blog"

#. translators: %s: expands to 'Yoast SEO Premium'.
#. translators: 1: Yoast WooCommerce SEO
#: src/integrations/admin/workouts-integration.php:315
#: js/dist/integrations-page.js:11
msgid "Activate %s"
msgstr "Attiva %s"

#. translators: %s: expands to 'Yoast SEO Premium'.
#: src/integrations/admin/workouts-integration.php:309
msgid "Update %s"
msgstr "Aggiorna %s"

#. translators: %s: expands to 'Yoast SEO Premium'.
#: src/integrations/admin/workouts-integration.php:302
msgid "Renew %s"
msgstr "Rinnova %s"

#: src/integrations/admin/workouts-integration.php:243
msgid "Get help activating your subscription"
msgstr "Ottieni assistenza per attivare il tuo abbonamento"

#. translators: 1: expands to 'Yoast SEO Premium', 2: Link start tag to the
#. page to update Premium, 3: Link closing tag.
#: src/integrations/admin/workouts-integration.php:237
msgid "It looks like you’re running an outdated and unactivated version of %1$s, please activate your subscription in %2$sMyYoast%3$s and update to the latest version (at least 17.7) to gain access to our updated workouts section."
msgstr "Sembra che tu stia utilizzando una versione obsoleta e non attiva di %1$s, per favore attiva il tuo abbonamento in %2$sMyYoast%3$s e aggiorna all'ultima versione per accedere alla nostra sessione aggiornata di workout."

#. translators: %s: expands to 'Yoast SEO Premium'.
#: src/integrations/admin/workouts-integration.php:233
msgid "Activate your subscription of %s"
msgstr "Attiva il tuo abbonamento a %s"

#. translators: 1: expands to 'Yoast SEO Premium', 2: Link start tag to the
#. page to update Premium, 3: Link closing tag.
#: src/integrations/admin/workouts-integration.php:224
msgid "It looks like you're running an outdated version of %1$s, please %2$supdate to the latest version (at least 17.7)%3$s to gain access to our updated workouts section."
msgstr "Sembra che tu stia eseguendo una versione obsoleta di %1$s, per favore %2$s aggiorna all'ultima versione (almeno 17.7)%3$s per accedere alla nostra sezione workout aggiornata."

#. translators: %s: expands to 'Yoast SEO Premium'.
#: src/integrations/admin/workouts-integration.php:221
msgid "Update to the latest version of %s"
msgstr "Aggiorna all'ultima versione di %s"

#: src/integrations/admin/workouts-integration.php:213
msgid "Renew your subscription"
msgstr "Rinnova il tuo abbonamento"

#. translators: %s: expands to 'Yoast SEO Premium'.
#: src/integrations/admin/workouts-integration.php:206
msgid "Accessing the latest workouts requires an updated version of %s (at least 17.7), but it looks like your subscription has expired. Please renew your subscription to update and gain access to all the latest features."
msgstr "L'accesso agli ultimi workout richiede una versione aggiornata di %s (almeno 17.7), ma sembra che il tuo abbonamento sia scaduto. Rinnova l'abbonamento per aggiornare e accedere a tutte le ultime funzionalità."

#. translators: %s: expands to 'Yoast SEO Premium'.
#: src/integrations/admin/workouts-integration.php:203
msgid "Renew your subscription of %s"
msgstr "Rinnova il tuo abbonamento a %s"

#. translators: 1: Link start tag to the first-time configuration, 2: Yoast
#. SEO, 3: Link closing tag.
#: src/integrations/admin/first-time-configuration-notice-integration.php:110
msgid "Get started quickly with the %1$s%2$s First-time configuration%3$s and configure Yoast SEO with the optimal SEO settings for your site!"
msgstr "Fai subito i primi passi con la %1$s%2$s configurazione iniziale %3$s e configura Yoast SEO con le impostazioni SEO ottimali per il tuo sito!"

#: inc/class-wpseo-replace-vars.php:1499
msgid "Current or first category title"
msgstr "Titolo della prima categoria o della categoria attuale "

#: inc/class-wpseo-replace-vars.php:1499
msgid "Category Title"
msgstr "Titolo della categoria"

#: inc/class-wpseo-replace-vars.php:1498
msgid "Replaced with the post content"
msgstr "Sostituito con il contenuto dell'articolo"

#: inc/class-wpseo-replace-vars.php:1498
msgid "Post Content"
msgstr "Contenuto dell'articolo"

#: inc/class-wpseo-replace-vars.php:1497
msgid "Replaced with the permalink"
msgstr "Sostituito dal permalink"

#: inc/class-wpseo-replace-vars.php:1496
msgid "Replaced with the last name of the author"
msgstr "Sostituito dal cognome dell'autore"

#: inc/class-wpseo-replace-vars.php:1496
msgid "Author last name"
msgstr "Cognome dell'autore"

#: inc/class-wpseo-replace-vars.php:1495
msgid "Replaced with the first name of the author"
msgstr "Sostituito dal nome dell'autore"

#: inc/class-wpseo-replace-vars.php:1495
msgid "Author first name"
msgstr "Nome dell'autore"

#: inc/class-wpseo-replace-vars.php:1494
msgid "Replaced with the day the post was published"
msgstr "Sostituito dal giorno in cui l'articolo è stato pubblicato"

#: inc/class-wpseo-replace-vars.php:1494
msgid "Post day"
msgstr "Giorno di pubblicazione"

#: inc/class-wpseo-replace-vars.php:1493
msgid "Replaced with the month the post was published"
msgstr "Sostituito dal mese in cui l'articolo è stato pubblicato"

#: inc/class-wpseo-replace-vars.php:1493
msgid "Post month"
msgstr "Mese di pubblicazione"

#: inc/class-wpseo-replace-vars.php:1492
msgid "Replaced with the year the post was published"
msgstr "Sostituito dall'anno in cui l'articolo è stato pubblicato"

#: inc/class-wpseo-replace-vars.php:1492
msgid "Post year"
msgstr "Anno di pubblicazione"

#: inc/class-wpseo-replace-vars.php:1491
msgid "Current day"
msgstr "Giorno corrente"

#: inc/class-wpseo-replace-vars.php:1490
msgid "Current month"
msgstr "Mese corrente"

#: inc/class-wpseo-replace-vars.php:1488
msgid "Current date"
msgstr "Data corrente"

#. translators: %1$s expands to an opening strong tag, %2$s expands to the
#. dependency name, %3$s expands to a closing strong tag, %4$s expands to an
#. opening anchor tag, %5$s expands to a closing anchor tag.
#: admin/class-suggested-plugins.php:111
msgid "It looks like you aren't using our %1$s%2$s addon%3$s. %4$sUpgrade today%5$s to unlock more tools and SEO features to make your products stand out in search results."
msgstr "Sembra che tu non stia usando il nostro %1$sadd-on %2$s%3$s. %4$sAggiorna subito%5$s per sbloccare nuovi strumenti e funzionalità SEO che faranno risaltare i tuoi prodotti nei risultati di ricerca."

#. translators: %1$s expands to an opening anchor tag for a link leading to the
#. Premium installation page, %2$s expands to a closing anchor tag.
#: src/presenters/admin/indexing-error-presenter.php:102
msgid "Below are the technical details for the error. See %1$sthis page%2$s for a more detailed explanation."
msgstr "Di seguito sono riportati i dettagli tecnici dell'errore. Dai un'occhiata a %1$squesta pagina%2$s per una spiegazione più dettagliata."

#: src/integrations/admin/workouts-integration.php:93
msgid "Workouts"
msgstr "Workout"

#: admin/views/class-yoast-integration-toggles.php:81
msgid "Improve the quality of your site search! Automatically helps your users find your cornerstone and most important content in your internal search results. It also removes noindexed posts & pages from your site’s search results."
msgstr "Migliora la qualità della ricerca all'interno del tuo sito! Aiuta automaticamente i tuoi utenti a trovare i contenuti Cornerstone e quelli più importanti nei risultati di ricerca interni. Rimuove anche gli articoli e le pagine non indicizzate dai risultati di ricerca del tuo sito."

#. translators: %s: Algolia.
#: admin/views/class-yoast-integration-toggles.php:83
msgid "Find out more about our %s integration."
msgstr "Scopri di più sulla nostra integrazione %s."

#: admin/views/class-yoast-feature-toggles.php:129
msgid "Read more about how internal linking can improve your site structure."
msgstr "Approfondisci come i link interni possono migliorare la struttura del tuo sito. "

#: admin/views/class-yoast-feature-toggles.php:128
msgid "Get relevant internal linking suggestions — while you’re writing! The link suggestions metabox shows a list of posts on your blog with similar content that might be interesting to link to. "
msgstr "Ottieni utili suggerimenti sui link interni mentre scrivi! La metabox con i suggerimenti dei link mostra una lista di articoli sul tuo blog che trattano contenuti simili e verso cui potrebbe interessarti aggiungere dei collegamenti. "

#: admin/views/class-yoast-feature-toggles.php:125 js/dist/new-settings.js:38
#: js/dist/new-settings.js:312
msgid "Link suggestions"
msgstr "Link suggeriti"

#: admin/views/class-yoast-feature-toggles.php:119
msgid "Find out how Insights can help you improve your content."
msgstr "Scopri come gli Approfondimenti (Insights) ti aiutano a migliorare il tuo contenuto."

#: admin/views/class-yoast-feature-toggles.php:118
msgid "Find relevant data about your content right in the Insights section in the Yoast SEO metabox. You’ll see what words you use most often and if they’re a match with your keywords! "
msgstr "Trova dati rilevanti per il tuo contenuto nella sezione Approfondimenti (Insights) della metabox di Yoast SEO. Potrai vedere quali parole usi più spesso e se corrispondono alle tue parole chiave! "

#: admin/views/class-yoast-feature-toggles.php:116 js/dist/block-editor.js:244
#: js/dist/block-editor.js:548 js/dist/classic-editor.js:229
#: js/dist/elementor.js:120 js/dist/new-settings.js:38
#: js/dist/new-settings.js:312
msgid "Insights"
msgstr "Approfondimenti (Insights)"

#. translators: %1$s expands to an opening anchor tag for a link leading to the
#. Premium installation page, %2$s expands to a closing anchor tag.
#. Translators: %1$s expands to an opening anchor tag for a link leading to the
#. Premium installation page, %2$s expands to a closing anchor tag.
#: src/presenters/admin/indexing-error-presenter.php:76
#: src/presenters/admin/indexing-failed-notification-presenter.php:77
msgid "Oops, something has gone wrong and we couldn't complete the optimization of your SEO data. Please make sure to activate your subscription in MyYoast by completing %1$sthese steps%2$s."
msgstr "Oops, qualcosa non ha funzionato e non è possibile completare l'ottimizzazione dei dati SEO. Attiva il tuo abbonamento a MyYoast seguendo %1$squeste istruzioni%2$s."

#: admin/views/redirects.php:22 js/dist/integrations-page.js:4
#: js/dist/integrations-page.js:7 js/dist/integrations-page.js:19
msgid "Unlock with Premium"
msgstr "Sblocca con Premium"

#. translators: %1$s expands to Yoast SEO
#: src/integrations/admin/addon-installation/dialog-integration.php:94
msgid "No %1$s plugins have been installed. You don't seem to own any active subscriptions."
msgstr "Nessun %1$s plugin è stato installato. Pare che tu non abbia nessun abbonamento attivo."

#. Translators: %s expands to the error message.
#: src/integrations/admin/addon-installation/installation-integration.php:192
msgid "Addon installation failed because of an error: %s."
msgstr "L'installazione dell'add-on non è riuscita a causa di un errore: %s."

#: src/integrations/admin/addon-installation/installation-integration.php:188
msgid "You are not allowed to install plugins."
msgstr "Non hai i permessi per installare i plugin."

#. Translators: %s expands to the name of the addon.
#: src/integrations/admin/addon-installation/installation-integration.php:184
msgid "Addon installed."
msgstr "Add-on installato."

#. Translators:%s expands to the error message.
#: src/integrations/admin/addon-installation/installation-integration.php:160
msgid "Addon activation failed because of an error: %s."
msgstr "L'attivazione dell'add-on non è riuscita a causa di un errore: %s."

#: src/integrations/admin/addon-installation/installation-integration.php:156
msgid "You are not allowed to activate plugins."
msgstr "Non hai i permessi per attivare i plugin."

#. Translators: %s expands to the name of the addon.
#: src/integrations/admin/addon-installation/installation-integration.php:154
msgid "Addon activated."
msgstr "Add-on attivato."

#. translators: %1$s expands to an anchor tag to the admin premium page, %2$s
#. expands to Yoast SEO Premium, %3$s expands to a closing anchor tag
#: src/integrations/admin/addon-installation/installation-integration.php:129
msgid "%1$s Continue to %2$s%3$s"
msgstr "%1$s Continua %2$s%3$s"

#: src/integrations/admin/addon-installation/installation-integration.php:106
msgid "Installing and activating addons"
msgstr "Installare e attivare gli add-on"

#. translators: %s expands to Yoast SEO Premium.
#: admin/class-admin.php:248
msgid "Required by %s"
msgstr "È necessario affinché %s funzioni correttamente"

#. Translators: %1$s resolves to Yoast SEO.
#: src/integrations/watchers/addon-update-watcher.php:95
msgid "Auto-updates are disabled based on this setting for %1$s."
msgstr "Gli aggiornamenti automatici sono disabilitati in base alle impostazioni che hai definito per %1$s."

#. Translators: %1$s resolves to Yoast SEO.
#: src/integrations/watchers/addon-update-watcher.php:85
msgid "Auto-updates are enabled based on this setting for %1$s."
msgstr "Gli aggiornamenti automatici sono abilitati in base alle impostazioni che hai definito per %1$s."

#: src/presenters/admin/badge-presenter.php:80
#: src/presenters/admin/badge-presenter.php:87
#: js/dist/externals/componentsNew.js:1081 js/dist/integrations-page.js:3
#: js/dist/integrations-page.js:6 js/dist/integrations-page.js:18
#: js/dist/new-settings.js:354
msgid "New"
msgstr "Nuovo"

#: src/exceptions/indexable/post-not-found-exception.php:16
msgid "The post could not be found."
msgstr "L'articolo non è stato trovato."

#. translators: %s is the reason given by WordPress.
#: src/exceptions/indexable/invalid-term-exception.php:21
msgid "The term is considered invalid. The following reason was given by WordPress: %s"
msgstr "Il termine è considerato non valido. WordPress indica come motivo: %s"

#: src/exceptions/indexable/term-not-found-exception.php:16
msgid "The term could not be found."
msgstr "Il termine non è stato trovato."

#: admin/class-yoast-form.php:1068 js/dist/general-page.js:52
msgid "This feature has been disabled since subsites never send tracking data."
msgstr "Questa funzione è stata disabilitata in quanto i sottositi non inviano mai dati di tracciamento."

#. translators: %1$s expands to an opening anchor tag, %2$s expands to an
#. closing anchor tag.
#: src/integrations/third-party/wpml-wpseo-notification.php:110
msgid "We notice that you have installed WPML. To make sure your canonical URLs are set correctly, %1$sinstall and activate the WPML SEO add-on%2$s as well!"
msgstr "Abbiamo rilevato che hai installato WPML. Per assicurarti che i tuoi URLs canonici siano impostati in modo corretto, %1$sinstalla e attiva anche il plugin add-on YWPML SEO%2$s!"

#: src/presenters/admin/indexing-notification-presenter.php:81
msgid "Because of a change in your category base setting, some of your SEO data needs to be reprocessed."
msgstr "Poichè ci sono stati dei cambiamenti nelle impostazioni dell'URL della categoria, alcuni dei tuoi dati SEO devono essere processati di nuovo."

#: admin/views/class-yoast-feature-toggles.php:190
msgid "Find out how a rich snippet can improve visibility and click-through-rate."
msgstr "Scopri come i rich snippet possono migliorare la visibilità del tuo sito e la tua percentuale di clic."

#: admin/views/class-yoast-feature-toggles.php:189 js/dist/new-settings.js:312
msgid "This adds an author byline and reading time estimate to the article’s snippet when shared on Slack."
msgstr "Questo aggiunge l'autore ed il tempo di lettura allo snippet dell'articolo quando viene condiviso su Slack"

#: admin/views/class-yoast-feature-toggles.php:187
msgid "Enhanced Slack sharing"
msgstr "Condivisione su Slack migliorata"

#. translators: 1: Expands to Yoast SEO
#: src/presenters/admin/indexing-notification-presenter.php:129
msgid "Wait for a week or so, until %1$s automatically processes most of your content in the background."
msgstr "Entro una settimana %1$s elaborerà automaticamente la maggior parte dei tuoi contenuti in background."

#. translators: %s expands to the reading time number, in minutes
#: src/presenters/slack/enhanced-data-presenter.php:55
msgid "%s minute"
msgid_plural "%s minutes"
msgstr[0] "%s minuto"
msgstr[1] "%s minuti"

#. translators: %s expands to the reading time number, in minutes
#: src/presenters/slack/enhanced-data-presenter.php:55
msgid "Est. reading time"
msgstr "Tempo di lettura stimato"

#: src/presenters/slack/enhanced-data-presenter.php:50
msgid "Written by"
msgstr "Scritto da"

#: inc/class-wpseo-admin-bar-menu.php:444
msgid "Google Rich Results Test"
msgstr "Test Google Rich Results"

#: src/presenters/admin/indexing-notification-presenter.php:84
msgid "Because of a change in your tag base setting, some of your SEO data needs to be reprocessed."
msgstr "Poiché ci sono stati dei cambiamenti nelle impostazioni dell'URL della categoria, alcuni dei tuoi dati SEO devono essere elaborati di nuovo."

#. translators: %s: 'Semrush'
#: admin/views/class-yoast-integration-toggles.php:71
msgid "The %s integration offers suggestions and insights for keywords related to the entered focus keyphrase."
msgstr "L'integrazione con %s offre suggerimenti e approfondimenti per le parole chiave correlate alla frase chiave principale inserita."

#. translators: %1$s expands to Yoast SEO
#: admin/views/tabs/network/integrations.php:27
msgid "This tab allows you to selectively disable %1$s integrations with third-party products for all sites in the network. By default all integrations are enabled, which allows site admins to choose for themselves if they want to toggle an integration on or off for their site. When you disable an integration here, site admins will not be able to use that integration at all."
msgstr "Questa scheda ti permette di disabilitare le integrazioni %1$s da servizi di terze parti per tutti i siti nella tua rete. L'impostazione predefinita per tutte le integrazioni è di essere abilitate, così da permettere agli amministratori del sito di scegliere autonomamente se mostrare o nascondere un'integrazione per il loro sito. Quando disattivi un'integrazione qui, gli amministratori del sito non potranno più usare quell'integrazione."

#: admin/class-admin.php:259
msgid "Activate your subscription"
msgstr "Attiva il tuo abbonamento"

#: src/presenters/admin/indexing-error-presenter.php:64
msgid "Oops, something has gone wrong and we couldn't complete the optimization of your SEO data. Please click the button again to re-start the process. "
msgstr "Oops, qualcosa non ha funzionato e non è possibile completare l'ottimizzazione dei dati SEO. Fai di nuovo clic sul pulsante per iniziare di nuovo il processo."

#: src/integrations/watchers/indexable-homeurl-watcher.php:97
msgid "All permalinks were successfully reset"
msgstr "Tutti i permalink sono stati reimpostati con successo."

#: src/presenters/admin/indexing-notification-presenter.php:96
msgid "You can speed up your site and get insight into your internal linking structure by letting us perform a few optimizations to the way SEO data is stored."
msgstr "Puoi rendere più veloce il tuo sito e ottenere informazioni sulla sua struttura dei link interni permettendoci di effettuare alcune ottimizzazioni al modo in cui i dati SEO sono memorizzati."

#: src/presenters/admin/indexing-notification-presenter.php:59
#: js/dist/general-page.js:48 js/dist/indexation.js:8
msgid "Start SEO data optimization"
msgstr "Inizia l'ottimizzazione dei dati SEO"

#: src/presenters/admin/indexing-list-item-presenter.php:42
msgid "Learn more about the benefits of optimized SEO data."
msgstr "Approfondisci i vantaggi dell'ottimizzazione dei dati SEO."

#: src/presenters/admin/indexing-list-item-presenter.php:40
msgid "You can speed up your site and get insight into your internal linking structure by letting us perform a few optimizations to the way SEO data is stored. If you have a lot of content it might take a while, but trust us, it's worth it."
msgstr "Puoi rendere più veloce il tuo sito e ottenere informazioni sulla sua struttura dei link interni permettendoci di effettuare alcune ottimizzazioni al modo in cui i dati SEO sono memorizzati. Se hai molti contenuti potrebbe volerci un po' di tempo, ma fidati, ne vale la pena."

#: src/presenters/admin/indexing-list-item-presenter.php:37
msgid "Optimize SEO Data"
msgstr "Ottimizza i dati SEO"

#: src/presenters/admin/indexing-error-presenter.php:71
#: src/presenters/admin/indexing-failed-notification-presenter.php:71
msgid "If the problem persists, please contact support."
msgstr "Se il problema permane, contatta il supporto."

#. Translators: %1$s expands to an opening anchor tag for a link leading to the
#. Yoast SEO tools page, %2$s expands to a closing anchor tag.
#: src/presenters/admin/indexing-failed-notification-presenter.php:59
msgid "Something has gone wrong and we couldn't complete the optimization of your SEO data. Please %1$sre-start the process%2$s."
msgstr "Qualcosa non ha funzionato e non è possibile completare l'ottimizzazione dei dati SEO. %1$sInizia di nuovo il processo%2$s."

#. translators: %s expands to a mailto support link.
#: inc/class-addon-manager.php:877
msgid "If you still need support and have an active subscription for this product, please email %s."
msgstr "Se hai ancora bisogno del nostro aiuto e hai un'abbonamento attivo per questo plugin, inviaci una email a %s."

#. translators: 1: expands to <a> that refers to the help page, 2: </a> closing
#. tag.
#: inc/class-addon-manager.php:874
msgid "You can probably find an answer to your question in our %1$shelp center%2$s."
msgstr "Probabilmente nella %1$ssezione Help%2$s del nostro sito trovi la risposta alla tua domanda."

#: inc/class-addon-manager.php:871
msgid "Need support?"
msgstr "Hai bisogno di aiuto?"

#. translators: %1$s: expands to an opening anchor tag, %2$s: expands to a
#. closing anchor tag
#: admin/views/class-yoast-feature-toggles.php:241
msgid "Disabling Yoast SEO's XML sitemaps will not disable WordPress' core sitemaps. In some cases, this %1$s may result in SEO errors on your site%2$s. These may be reported in Google Search Console and other tools."
msgstr "La disabilitazione delle sitemap XML di Yoast SEO non disabilita le sitemap di WordPress. In alcuni casi, questo %1$s può causare errori di SEO sul tuo sito%2$s. Questi possono essere segnalati in Google Search Console e altri strumenti."

#. translators: 1: Yoast SEO, 2: translated version of "Off"
#: admin/views/class-yoast-feature-toggles.php:158
msgid "The advanced section of the %1$s meta box allows a user to remove posts from the search results or change the canonical. The settings in the schema tab allows a user to change schema meta data for a post. These are things you might not want any author to do. That's why, by default, only editors and administrators can do this. Setting to \"%2$s\" allows all users to change these settings."
msgstr "La sezione delle impostazioni avanzate del meta-box di %1$s permette all'utente di rimuovere gli articoli dai risultati di ricerca o di cambiare l'URL canonico. Le impostazioni nella scheda schema permettono all'utente di cambiare i metadati schema per un articolo. Queste sono azioni che è desiderabile che nessun autore faccia. Ecco perché, in modalità predefinita, solo gli editori e gli amministratori possono farlo. L'impostazione su \"%2$s\" permette a tutti gli utenti di cambiare queste impostazioni."

#: admin/views/class-yoast-feature-toggles.php:154
msgid "Security: no advanced or schema settings for authors"
msgstr "Sicurezza: non c'è alcuna impostazione avanzata o schema per gli autori"

#: src/config/schema-types.php:159
msgid "Report"
msgstr "Rapporto"

#: src/config/schema-types.php:155
msgid "Tech Article"
msgstr "Articolo tecnico"

#: src/config/schema-types.php:151
msgid "Scholarly Article"
msgstr "Articolo scolastico"

#: src/config/schema-types.php:147
msgid "Satirical Article"
msgstr "Articolo di satira"

#: src/config/schema-types.php:143
msgid "Advertiser Content Article"
msgstr "Articolo con contenuti pubblicitari"

#: src/config/schema-types.php:139
msgid "News Article"
msgstr "Articolo di notizie"

#: src/config/schema-types.php:135
msgid "Social Media Posting"
msgstr "Pubblicazione sui social media"

#: src/config/schema-types.php:127
msgid "Article"
msgstr "Articolo"

#: src/config/schema-types.php:104
msgid "Search Results Page"
msgstr "Pagina dei risultati della ricerca "

#: src/config/schema-types.php:100
msgid "Real Estate Listing"
msgstr "Annunci immobiliari"

#: src/config/schema-types.php:96
msgid "Checkout Page"
msgstr "Pagina del pagamento"

#: src/config/schema-types.php:92
msgid "Collection Page"
msgstr "Pagina della collezione"

#: src/config/schema-types.php:88
msgid "Medical Web Page"
msgstr "Pagina web per servizi medici"

#: src/config/schema-types.php:84
msgid "Contact Page"
msgstr "Pagina dei contatti"

#: src/config/schema-types.php:80
msgid "Profile Page"
msgstr "Pagina del profilo"

#: src/config/schema-types.php:76
msgid "QA Page"
msgstr "Pagina QA (domande e risposte)"

#: src/config/schema-types.php:72
msgid "FAQ Page"
msgstr "Pagina per le FAQ (domande frequenti)"

#: src/config/schema-types.php:68
msgid "About Page"
msgstr "Pagina About"

#: src/config/schema-types.php:64 js/dist/block-editor.js:527
#: js/dist/classic-editor.js:512 js/dist/elementor.js:389
msgid "Item Page"
msgstr "Pagina elemento"

#: src/config/schema-types.php:60
msgid "Web Page"
msgstr "Pagina web"

#. translators: 1: Yoast SEO
#: admin/views/class-yoast-feature-toggles.php:170
msgid "Allow us to track some data about your site to improve our plugin."
msgstr "Permettici di tracciare qualche dato del tuo sito per migliorare il nostro plugin."

#: admin/views/class-yoast-feature-toggles.php:165
#: admin/views/class-yoast-feature-toggles.php:166 js/dist/new-settings.js:38
#: js/dist/new-settings.js:309
msgid "Usage tracking"
msgstr "Tracciamento d'uso"

#: src/presenters/admin/indexing-notification-presenter.php:75
msgid "Because of a change in your permalink structure, some of your SEO data needs to be reprocessed."
msgstr "Poichè ci sono stati dei cambiamenti nella struttura dei permalink, alcuni dei tuoi dati SEO devono essere processati di nuovo."

#: src/presenters/admin/indexing-notification-presenter.php:78
msgid "Because of a change in your home URL setting, some of your SEO data needs to be reprocessed."
msgstr "Poichè ci sono stati dei cambiamenti nelle impostazioni dell'URL della home, alcuni dei tuoi dati SEO devono essere processati di nuovo."

#. translators: %1$s expands to Yoast.
#: src/integrations/blocks/block-categories.php:45
msgid "%1$s Internal Linking Blocks"
msgstr "Internal Linking Block di %1$s"

#. translators: 1: Link to the Yoast help center, 2: Link closing tag.
#: src/services/health-check/links-table-reports.php:100
msgid "%1$sFind out how to solve this problem on our help center%2$s."
msgstr "%1$sScopri come risolvere questo problema nella nostra sezione di aiuto%2$s."

#: src/services/health-check/links-table-reports.php:58
msgid "The text link counter feature is not working as expected"
msgstr "Il contatore dei link inseriti nel testo non funziona come previsto"

#. translators: 1: Link to the Yoast SEO blog, 2: Link closing tag.
#: src/services/health-check/links-table-reports.php:73
msgid "The text link counter helps you improve your site structure. %1$sFind out how the text link counter can enhance your SEO%2$s."
msgstr "Il contatore dei link inseriti nel testo ti aiuta a migliorare la struttura del tuo sito. %1$sScopri come il contatore dei link inseriti nel testo può migliorare la tua strategia SEO%2$s."

#: src/services/health-check/links-table-reports.php:45
msgid "The text link counter is working as expected"
msgstr "Il contatore dei link nel testo sta funzionando correttamente"

#. translators: %1$s: Link to article about text links, %2$s: Anchor closing
#. tag, %3$s: Emphasis open tag, %4$s: Emphasis close tag
#: admin/class-yoast-columns.php:52
msgid "The links columns show the number of articles on this site linking %3$sto%4$s this article and the number of URLs linked %3$sfrom%4$s this article. Learn more about %1$show to use these features to improve your internal linking%2$s, which greatly enhances your SEO."
msgstr "Le colonne dei collegamenti mostrano il numero di articoli su questo sito che collega %3$sa%4$s questo articolo e il numero di URL linkati %3$sda%4$s questo articolo. Ulteriori informazioni su %1$s come utilizzare queste funzionalità per migliorare i tuoi collegamenti interni%2$s, che migliorerà notevolmente la tua strategia SEO."

#. translators: %1$s: Link to article about content analysis, %2$s: Anchor
#. closing
#: admin/class-yoast-columns.php:43
msgid "We've written an article about %1$show to use the SEO score and Readability score%2$s."
msgstr "Abbiamo scritto un articolo su %1$scome utilizzare il punteggio SEO e il punteggio di leggibilità%2$s."

#. translators: %1$s: Yoast SEO
#: admin/class-yoast-columns.php:36
msgid "%1$s adds several columns to this page."
msgstr "%1$s aggiunge diverse colonne a questa pagina."

#: src/presenters/admin/search-engines-discouraged-presenter.php:41
msgid "I don't want this site to show in the search results."
msgstr "Non voglio che questo sito venga mostrato nei risultati di ricerca."

#. translators: 1: Link start tag to the WordPress Reading Settings page, 2:
#. Link closing tag.
#: src/presenters/admin/search-engines-discouraged-presenter.php:36
msgid "If you want search engines to show this site in their results, you must %1$sgo to your Reading Settings%2$s and uncheck the box for Search Engine Visibility."
msgstr "Se vuoi che i motori di ricerca mostrino questo sito nei loro risultati, %1$svai alle impostazioni di lettura%2$s e deseleziona la casella Visibilità ai motori di ricerca."

#: src/presenters/admin/migration-error-presenter.php:64
msgid "Show debug information"
msgstr "Mostra le informazioni di debug"

#. translators: %s: Yoast SEO.
#: src/presenters/admin/migration-error-presenter.php:58
msgid "Your site will continue to work normally, but won't take full advantage of %s."
msgstr "Il tuo sito continuerà a funzionare normalmente, ma non sfrutterà al massimo %s."

#. translators: %s: Yoast SEO.
#: src/presenters/admin/migration-error-presenter.php:47
msgid "%s had problems creating the database tables needed to speed up your site."
msgstr "%s ha riscontrato problemi durante la creazione delle tabelle del database necessarie per rendere più veloce il tuo sito."

#: src/presenters/admin/indexing-notification-presenter.php:117
msgid " We estimate this will take less than a minute."
msgstr "Stimiamo che ci vorrà meno di un minuto."

#: src/presenters/admin/indexing-notification-presenter.php:121
msgid " We estimate this will take a couple of minutes."
msgstr "Stimiamo che ci vorranno un paio di minuti."

#. translators: 1: Link to article about indexation command, 2: Anchor closing
#. tag, 3: Link to WP CLI.
#: src/presenters/admin/indexing-notification-presenter.php:136
msgid "%1$sRun the indexation process on your server%2$s using %3$sWP CLI%2$s."
msgstr "%1$sEsegui il processo di indicizzazione sul tuo server%2$s usando %3$sWP CLI%2$s."

#: src/presenters/admin/indexing-notification-presenter.php:124
msgid " We estimate this could take a long time, due to the size of your site. As an alternative to waiting, you could:"
msgstr "Stimiamo che ciò potrebbe richiedere molto tempo a causa delle dimensioni del tuo sito. Invece di aspettare, potresti:"

#. translators: %1$s: link to help article about solving table issue. %2$s: is
#. anchor closing.
#: src/presenters/admin/migration-error-presenter.php:52
msgid "Please read %1$sthis help article%2$s to find out how to resolve this problem."
msgstr "Leggi %1$squesto articolo nell'Help%2$s per capire come risolvere il problema."

#: inc/class-wpseo-replace-vars.php:1486
msgid "Replaced with the term ancestors hierarchy"
msgstr "Sostituito con il termine gerarchico progenitore"

#: inc/class-wpseo-replace-vars.php:1486
msgid "Term hierarchy"
msgstr "Gerarchia dei termini"

#. translators: 1: Yoast SEO
#: admin/views/class-yoast-feature-toggles.php:181
msgid "This %1$s REST API endpoint gives you all the metadata you need for a specific URL. This will make it very easy for headless WordPress sites to use %1$s for all their SEO meta output."
msgstr "L’endpoint REST API di %1$s ti fornisce tutti i metadati di cui hai bisogno per uno specifico URL. Questo renderà molto facile per i siti WordPress headless usare %1$s per tutti i loro meta output SEO."

#: admin/views/class-yoast-feature-toggles.php:177
msgid "REST API: Head endpoint"
msgstr "REST API: head endpoint"

#. translators: 1: link open tag; 2: link close tag.
#: src/services/health-check/default-tagline-reports.php:63
msgid "%1$sYou can change the tagline in the customizer%2$s."
msgstr "%1$sPuoi cambiare il motto in Personalizza%2$s."

#: src/services/health-check/default-tagline-reports.php:45
msgid "You still have the default WordPress tagline. Even an empty one is probably better."
msgstr "Stai ancora usando il motto predefinito di WordPress. Puoi anche lasciare vuoto, che è meglio che usare il motto predefinito."

#: src/services/health-check/default-tagline-reports.php:43
msgid "You should change the default WordPress tagline"
msgstr "Dovresti cambiare il motto predefinito di WordPress."

#: src/services/health-check/default-tagline-reports.php:32
msgid "You are using a custom tagline or an empty one."
msgstr "Stai usando un motto personalizzato oppure hai lasciato lo spazio vuoto."

#: src/services/health-check/default-tagline-reports.php:30
msgid "You changed the default WordPress tagline"
msgstr "Hai cambiato il motto predefinito di WordPress"

#: src/services/health-check/page-comments-reports.php:32
msgid "Comments on your posts are displayed on a single page. This is just like we'd suggest it. You're doing well!"
msgstr "I commenti ai tuoi articoli sono visualizzati in una singola pagina. Questo è proprio quello che suggeriamo. Ottimo!"

#. translators: %s expands to '/%postname%/'
#: src/services/health-check/postname-permalink-reports.php:58
msgid "It's highly recommended to have your postname in the URL of your posts and pages. Consider setting your permalink structure to %s."
msgstr "Ti consigliamo vivamente di inserire il nome degli articoli e delle pagine nel loro URL. Considera di impostare la struttura dei permalink su %s."

#: src/services/health-check/postname-permalink-reports.php:43
msgid "You do not have your postname in the URL of your posts and pages"
msgstr "Non hai usato il nome degli articoli e delle pagine nel loro URL."

#: src/services/health-check/postname-permalink-reports.php:32
msgid "You do have your postname in the URL of your posts and pages."
msgstr "Hai usato il nome degli articoli e delle pagine nel loro URL."

#: src/services/health-check/postname-permalink-reports.php:30
msgid "Your permalink structure includes the post name"
msgstr "La struttura del permalink include il nome dell’articolo"

#: src/services/health-check/page-comments-reports.php:45
msgid "Comments on your posts break into multiple pages. As this is not needed in 999 out of 1000 cases, we recommend you disable it. To fix this, uncheck \"Break comments into pages...\" on the Discussion Settings page."
msgstr "I commenti sui tuoi articoli si dividono in più pagine. Poiché questo non è necessario in 999 casi su 1000, ti consigliamo di disabilitarlo. Per risolvere questo problema, deseleziona \"Dividi i commenti in pagine...\" nel menu Impostazioni>Discussione."

#: src/services/health-check/page-comments-reports.php:43
msgid "Comments break into multiple pages"
msgstr "I commenti si dividono in più pagine"

#: src/services/health-check/page-comments-reports.php:30
msgid "Comments are displayed on a single page"
msgstr "I commenti vengono visualizzati su una singola pagina"

#: src/helpers/post-helper.php:112
msgid "No title"
msgstr "Nessun titolo"

#. translators: 1: Start of a paragraph beginning with the Yoast icon, 2:
#. Expands to 'Yoast SEO', 3: Paragraph closing tag.
#: src/services/health-check/report-builder.php:201
msgid "%1$sThis was reported by the %2$s plugin%3$s"
msgstr "%1$sQuesto è stato riportato dal plugin %2$s%3$s"

#. translators: 1: Opening tag of the link to the discussion settings page, 2:
#. Link closing tag.
#: src/services/health-check/page-comments-reports.php:58
msgid "%1$sGo to the Discussion Settings page%2$s"
msgstr "%1$sVai al menu Impostazioni>Discussione%2$s"

#: admin/metabox/class-metabox.php:194
msgid "If you want to apply advanced <code>meta</code> robots settings for this page, please define them in the following field."
msgstr "Se vuoi applicare le impostazioni avanzate per i <code>meta</code> robot in questa pagina, definiscile nel campo seguente."

#. translators: 1: Link start tag to the Firefox website, 2: Link start tag to
#. the Chrome website, 3: Link start tag to the Edge website, 4: Link closing
#. tag.
#: admin/metabox/class-metabox.php:150 admin/taxonomy/class-taxonomy.php:113
msgid "The browser you are currently using is unfortunately rather dated. Since we strive to give you the best experience possible, we no longer support this browser. Instead, please use %1$sFirefox%4$s, %2$sChrome%4$s or %3$sMicrosoft Edge%4$s."
msgstr "Il browser che stai usando attualmente è obsoleto. Poiché ci teniamo a fornirti la miglior esperienza possibile, non supportiamo più questo browser. Al suo posto, puoi usare %1$sFirefox%4$s, %2$sChrome%4$s o %3$sMicrosoft Edge%4$s."

#. translators: %1$s expands to Yoast SEO academy
#. translators: %1$s expands to "Yoast SEO academy".
#: src/presenters/admin/sidebar-presenter.php:137 js/dist/general-page.js:3
#: js/dist/new-settings.js:3 js/dist/support.js:3
msgid "Check out %1$s"
msgstr "Vai alla cassa di %1$s"

#: src/presenters/admin/sidebar-presenter.php:130 js/dist/general-page.js:2
#: js/dist/new-settings.js:2 js/dist/support.js:2
msgid "We have both free and premium online courses to learn everything you need to know about SEO."
msgstr "Abbiamo corsi online, sia gratuiti sia a pagamento per imparare tutto ciò che devi sapere riguardo la SEO."

#. translators: %1$s expands to Yoast SEO academy, which is a clickable link.
#. translators: %1$s expands to "Yoast SEO" academy, which is a clickable link.
#: src/presenters/admin/sidebar-presenter.php:128 js/dist/general-page.js:2
#: js/dist/new-settings.js:2 js/dist/support.js:2
msgid "Want to learn SEO from Team Yoast? Check out our %1$s!"
msgstr "Vuoi imparare la SEO dal Team di Yoast? Vai alla cassa della nostra %1$s"

#: src/presenters/admin/sidebar-presenter.php:120 js/dist/general-page.js:2
#: js/dist/new-settings.js:2 js/dist/support.js:2
msgid "Learn SEO"
msgstr "Impara la SEO"

#. translators: %s expands to Yoast SEO
#: admin/views/tabs/tool/wpseo-import.php:39
msgid "%s settings to import:"
msgstr "Impostazioni di %s da importare:"

#. translators: 1: expands to Yoast SEO, 2: expands to Import settings.
#: admin/views/tabs/tool/wpseo-import.php:23
msgid "Import settings from another %1$s installation by pasting them here and clicking \"%2$s\"."
msgstr "Importa le impostazioni da un'altra installazione di %1$s installation incollandole qui e facendo clic su \"%2$s\"."

#. translators: %1$s expands to Yoast SEO
#: admin/class-export.php:72
msgid "Your %1$s settings:"
msgstr "Le tue impostazioni di %1$s:"

#: admin/metabox/class-metabox.php:427 js/dist/block-editor.js:573
#: js/dist/elementor.js:540 js/dist/new-settings.js:33
#: js/dist/new-settings.js:38 js/dist/new-settings.js:42
#: js/dist/new-settings.js:71 js/dist/new-settings.js:254
msgid "Schema"
msgstr "Schema"

#: admin/admin-settings-changed-listener.php:85
msgid "Settings saved."
msgstr "Impostazioni salvate."

#. translators: Hidden accessibility text.
#: admin/views/partial-notifications-template.php:47
msgid "Show this item."
msgstr "Mostra questa voce."

#. translators: Hidden accessibility text.
#: admin/views/partial-notifications-template.php:39
msgid "Hide this item."
msgstr "Nascondi questa voce"

#. translators: %d expands the amount of hidden notifications.
#: admin/views/partial-notifications-errors.php:25
#: admin/views/partial-notifications-warnings.php:25
msgid "You have %d hidden notification:"
msgid_plural "You have %d hidden notifications:"
msgstr[0] "Hai %d notifica nascosta:"
msgstr[1] "Hai %d notifiche nascoste:"

#: src/helpers/score-icon-helper.php:84
msgid "Focus keyphrase not set"
msgstr "La frase chiave non è stata impostata"

#. translators: %1$s: amount of errors, %2$s: the admin page title
#: admin/class-yoast-input-validation.php:65
msgid "The form contains %1$s error. %2$s"
msgid_plural "The form contains %1$s errors. %2$s"
msgstr[0] "Il modulo contiene %1$s errore. %2$s"
msgstr[1] "Il modulo contiene %1$s errori. %2$s"

#. translators: %s expands to the score
#: admin/statistics/class-statistics-service.php:216
#: admin/statistics/class-statistics-service.php:221
#: admin/statistics/class-statistics-service.php:226
msgid "Posts with the SEO score: %s"
msgstr "Articoli con il punteggio SEO: %s"

#. translators: %s: expends to Yoast SEO
#: admin/class-admin.php:354
msgid "%s video tutorial"
msgstr "%s video tutorial"

#: inc/class-wpseo-rank.php:191
msgid "Post Noindexed"
msgstr "Articoli non indicizzati"

#: inc/class-wpseo-rank.php:171
msgid "No Focus Keyphrase"
msgstr "Manca la frase chiave"

#. translators: %s expands to the SEO score
#: inc/class-wpseo-rank.php:170 inc/class-wpseo-rank.php:175
#: inc/class-wpseo-rank.php:180 inc/class-wpseo-rank.php:185
#: inc/class-wpseo-rank.php:190
msgid "SEO: %s"
msgstr "SEO: %s"

#. Translators: %1$s: expands to opening anchor tag, %2$s expands to closing
#. anchor tag.
#: admin/google_search_console/views/gsc-display.php:39
msgid "To view your current crawl errors, %1$splease visit Google Search Console%2$s."
msgstr "Per visualizzare i tuoi errori di crawl attuali, %1$svisita Google Search Console%2$s."

#. Translators: %1$s: expands to opening anchor tag, %2$s expands to closing
#. anchor tag.
#: admin/google_search_console/views/gsc-display.php:32
msgid "Google has discontinued its Crawl Errors API. Therefore, any possible crawl errors you might have cannot be displayed here anymore. %1$sRead our statement on this for further information%2$s."
msgstr "Google ha sospeso la Crawl Errors API. Perciò, gli eventuali errori di crawl che potresti avere non possono più essere visualizzati qui. %1$sLeggi il nostro articolo per avere ulteriori informazioni%2$s."

#: src/integrations/admin/first-time-configuration-integration.php:480
#: src/integrations/admin/first-time-configuration-integration.php:493
#: js/dist/new-settings.js:324
msgid "Organization"
msgstr "Organizzazione"

#. translators: %1$s is a link start tag to the Search Appearance settings,
#. %2$s is the link closing tag.
#: admin/class-schema-person-upgrade-notification.php:66
msgid "You have previously set your site to represent a person. We’ve improved our functionality around Schema and the Knowledge Graph, so you should go in and %1$scomplete those settings%2$s."
msgstr "In precedenza hai impostato il sito per rappresentare una persona. Abbiamo migliorato le nostre funzionalità riguardanti i dati Schema e il Knowledge Graph, per questo dovresti andare a %1$scompletare queste impostazioni%2$s."

#: admin/class-admin.php:317
#: src/user-meta/framework/additional-contactmethods/wikipedia.php:28
msgid "(if one exists)"
msgstr "(se esiste)"

#: admin/class-admin.php:317
#: src/user-meta/framework/additional-contactmethods/wikipedia.php:28
msgid "Wikipedia page about you"
msgstr "La tua pagina Wikipedia"

#: admin/class-admin.php:316
#: src/user-meta/framework/additional-contactmethods/youtube.php:28
msgid "YouTube profile URL"
msgstr "URL del profilo YouTube"

#: admin/class-admin.php:314
#: src/user-meta/framework/additional-contactmethods/tumblr.php:28
msgid "Tumblr profile URL"
msgstr "URL del profilo Tumblr"

#: admin/class-admin.php:313
#: src/user-meta/framework/additional-contactmethods/soundcloud.php:28
msgid "SoundCloud profile URL"
msgstr "URL del profilo SoundCloud"

#: admin/class-admin.php:311
#: src/user-meta/framework/additional-contactmethods/myspace.php:28
msgid "MySpace profile URL"
msgstr "URL del profilo MySpace"

#: src/generators/schema/article.php:141
msgid "Uncategorized"
msgstr "Senza categoria"

#: admin/class-admin.php:312
#: src/user-meta/framework/additional-contactmethods/pinterest.php:28
msgid "Pinterest profile URL"
msgstr "URL del profilo di Pinterest "

#: admin/class-admin.php:310
#: src/user-meta/framework/additional-contactmethods/linkedin.php:28
msgid "LinkedIn profile URL"
msgstr "URL del profilo LinkedIn "

#: admin/class-admin.php:309
#: src/user-meta/framework/additional-contactmethods/instagram.php:28
msgid "Instagram profile URL"
msgstr "URL del profilo Instagram "

#: inc/class-my-yoast-api-request.php:140
msgid "No JSON object was returned."
msgstr "Non è stato restituito nessun oggetto JSON. "

#. translators: Hidden accessibility text.
#: src/integrations/admin/link-count-columns-integration.php:149
msgid "Received internal links"
msgstr "Link interni ricevuti"

#. translators: Hidden accessibility text.
#: src/integrations/admin/link-count-columns-integration.php:141
msgid "Outgoing internal links"
msgstr "Link interni in uscita"

#: admin/class-meta-columns.php:132 js/dist/block-editor.js:170
#: js/dist/classic-editor.js:155 js/dist/editor-modules.js:291
#: js/dist/elementor.js:510 js/dist/wincher-dashboard-widget.js:117
msgid "Keyphrase"
msgstr "Frase chiave"

#. translators: 1: Yoast SEO.
#: src/services/health-check/links-table-reports.php:87
msgid "For this feature to work, %1$s needs to create a table in your database. We were unable to create this table automatically."
msgstr "Perché questa opzione funzioni, %1$s deve creare una tabella nel tuo database. Non è stato possibile creare questa tabella in automatico."

#. translators: %1$s expands to the requested url
#: admin/exceptions/class-file-size-exception.php:40
msgid "Cannot get the size of %1$s because of unknown reasons."
msgstr "Impossibile recuperare la dimensione di %1$s per motivi sconosciuti."

#. translators: %1$s expands to the requested url
#: admin/exceptions/class-file-size-exception.php:23
msgid "Cannot get the size of %1$s because it is hosted externally."
msgstr "Impossibile recuperare la dimensione di %1$s perché è ospitata esternamente."

#. translators: %s expands to the current page number
#: src/generators/breadcrumbs-generator.php:424
msgid "Page %s"
msgstr "Pagina %s"

#. translators: %1$s expands to the method name. %2$s expands to the class name
#: src/exceptions/missing-method.php:24
msgid "Method %1$s() does not exist in class %2$s"
msgstr "Il metodo %1$s() non esiste nella classe %2$s"

#. translators: %s expands to Yoast SEO Premium
#: admin/watchers/class-slug-change-watcher.php:226
msgid "With %s, you can easily create such redirects."
msgstr "Con %s, puoi facilmente creare reindirizzamenti (redirects)."

#: admin/views/tabs/tool/wpseo-import.php:15
msgid "Import of settings is only supported on servers that run PHP 5.3 or higher."
msgstr "L'importazione delle impostazioni è supportata solo sui server che eseguono PHP 5.3 o versioni successive."

#. translators: %1$s expands to Yoast SEO
#: admin/views/tabs/tool/wpseo-export.php:26
msgid "Export your %1$s settings here, to copy them on another site."
msgstr "Esporta le impostazioni di %1$s qui, per poi copiarle in un altro sito."

#: admin/import/class-import-settings.php:85
msgid "No settings found."
msgstr "Nessuna impostazione trovata."

#. translators: %1$s expands to Yoast SEO, %2$s expands to Yoast.com
#: admin/class-export.php:97
msgid "These are settings for the %1$s plugin by %2$s"
msgstr "Queste sono le impostazioni per il plugin %1$s di %2$s"

#. translators: %1$s expands to Import settings
#: admin/class-export.php:61
msgid "Copy all these settings to another site's %1$s tab and click \"%1$s\" there."
msgstr "Copia tutte queste impostazioni nella scheda %1$s di un altro sito e fai clic \"%1$s\" lì."

#: admin/class-export.php:54
msgid "You do not have the required rights to export settings."
msgstr "Non hai i diritti necessari per esportare le impostazioni."

#. translators: %s expands to Yoast SEO Premium
#. translators: %s expands to "Yoast SEO" Premium
#: admin/class-premium-upsell-admin-block.php:125 js/dist/block-editor.js:34
#: js/dist/classic-editor.js:19 js/dist/elementor.js:19
#: js/dist/externals-components.js:200 js/dist/general-page.js:15
#: js/dist/integrations-page.js:49 js/dist/new-settings.js:15
#: js/dist/post-edit.js:13 js/dist/support.js:15 js/dist/term-edit.js:13
msgid "Upgrade to %s"
msgstr "Passa a %s"

#: admin/class-admin-init.php:354
msgid "Learn about why permalinks are important for SEO."
msgstr "Approfondisci il motivo per cui i permalink sono così importanti per la SEO. "

#. translators: %1$s and %2$s expand to <em> items to emphasize the word in the
#. middle.
#: admin/class-admin-init.php:348
msgid "Changing your permalinks settings can seriously impact your search engine visibility. It should almost %1$s never %2$s be done on a live website."
msgstr "Cambiare le impostazioni dei permalink può influenzare la visibilità sui motori di ricerca. Non dovresti quasi %1$s mai %2$s farlo quando un sito è online."

#: admin/class-admin-init.php:345
msgid "WARNING:"
msgstr "ATTENZIONE:"

#: admin/views/tabs/network/features.php:95
#: admin/views/tabs/network/integrations.php:82
#: src/integrations/admin/crawl-settings-integration.php:251
msgid "Disable"
msgstr "Disattiva"

#: admin/views/tabs/network/features.php:94
#: admin/views/tabs/network/integrations.php:81
#: src/integrations/admin/crawl-settings-integration.php:249
msgid "Allow Control"
msgstr "Permetti il controllo"

#. translators: %s expands to Yoast SEO
#: admin/views/tabs/network/crawl-settings.php:24
#: admin/views/tabs/network/features.php:27
msgid "This tab allows you to selectively disable %s features for all sites in the network. By default all features are enabled, which allows site admins to choose for themselves if they want to toggle a feature on or off for their site. When you disable a feature here, site admins will not be able to use that feature at all."
msgstr "Questa scheda ti permette di disattivare in modo selettivo le funzionalità di %s per tutti i siti del network. In modalità predefinita, tutte le funzionalità sono attivate: questo permette agli amministratori di scegliere quali funzioni attivare o meno per i propri siti. Quando disattivi una funzionalità qui, gli amministratori dei siti non potranno usarla per nulla."

#. translators: %s: argument name
#: admin/views/class-yoast-feature-toggle.php:161
msgid "%s is a required feature toggle argument."
msgstr "%s è un argomento della funzionalità di attivazione obbligatorio."

#: admin/class-yoast-form.php:1064 js/dist/general-page.js:52
msgid "This feature has been disabled by the network admin."
msgstr "Questa funzionalità è stata disattivata dall'amministratore del network."

#. translators: Hidden accessibility text.
#: admin/class-meta-columns.php:197
msgid "Focus keyphrase not set."
msgstr "La frase chiave non è stata impostata."

#. translators: %s expands to Yoast SEO Premium
#: admin/class-premium-popup.php:81
#: admin/watchers/class-slug-change-watcher.php:230
msgid "Get %s"
msgstr "Passa a %s"

#: inc/class-wpseo-admin-bar-menu.php:887
msgid "There is a new notification."
msgid_plural "There are new notifications."
msgstr[0] "C'è una nuova notifica. "
msgstr[1] "Ci sono nuove notifiche. "

#: inc/options/class-wpseo-option-titles.php:949
msgid "Colon"
msgstr "Due punti"

#. translators: %1$s expands to Yoast SEO, %2$s: 'SEO' plugin name of possibly
#. conflicting plugin with regard to the creation of duplicate SEO meta.
#: admin/class-plugin-conflict.php:90
msgid "Both %1$s and %2$s manage the SEO of your site. Running two SEO plugins at the same time is detrimental."
msgstr "Sia %1$s sia %2$s gestiscono la SEO del tuo sito. Avere due plugin SEO attivi contemporaneamente può esere dannoso."

#. translators: %d expands to the number of minute/minutes.
#. translators: %d expands to the number of minutes.
#: src/integrations/blocks/structured-data-blocks.php:146
#: js/dist/how-to-block.js:8 js/dist/how-to-block.js:14
msgid "%d minute"
msgid_plural "%d minutes"
msgstr[0] "%d minuto"
msgstr[1] "%d minuti"

#. translators: %d expands to the number of hour/hours.
#. translators: %d expands to the number of hours.
#: src/integrations/blocks/structured-data-blocks.php:139
#: js/dist/how-to-block.js:7 js/dist/how-to-block.js:13
msgid "%d hour"
msgid_plural "%d hours"
msgstr[0] "%d ora"
msgstr[1] "%d ore"

#. translators: %d expands to the number of day/days.
#. translators: %d expands to the number of days.
#: src/integrations/blocks/structured-data-blocks.php:132
#: js/dist/block-editor.js:149 js/dist/classic-editor.js:134
#: js/dist/editor-modules.js:270 js/dist/elementor.js:489
#: js/dist/how-to-block.js:6 js/dist/how-to-block.js:12
#: js/dist/wincher-dashboard-widget.js:43
msgid "%d day"
msgid_plural "%d days"
msgstr[0] "%d giorno"
msgstr[1] "%d giorni"

#: blocks/structured-data-blocks/how-to/block.json
msgctxt "block description"
msgid "Create a How-to guide in an SEO-friendly way. You can only use one How-to block per post."
msgstr "Crea una guida How-to in una modalità SEO-friendly. Puoi usare solo un blocco How-to per articolo."

#. translators: %1$s expands to Yoast.
#: src/integrations/blocks/block-categories.php:37
msgid "%1$s Structured Data Blocks"
msgstr "Blocchi di Dati Strutturati di %1$s"

#: src/integrations/blocks/structured-data-blocks.php:197
#: js/dist/how-to-block.js:5 js/dist/how-to-block.js:16
msgid "Time needed:"
msgstr "Tempo richiesto:"

#: inc/class-wpseo-admin-bar-menu.php:439
msgid "Check links to this URL"
msgstr "Controlla i link a questo URL"

#: inc/class-wpseo-admin-bar-menu.php:510
msgid "How to"
msgstr "How to"

#: admin/pages/network.php:31
msgid "Restore Site"
msgstr "Ripristina il sito web"

#: admin/menu/class-network-admin-menu.php:34
msgid "Network Settings"
msgstr "Impostazioni di rete"

#: admin/class-yoast-network-admin.php:276
msgid "You are not allowed to perform this action."
msgstr "Non sei autorizzato a eseguire questa azione."

#. translators: %s: error message
#: admin/class-yoast-network-admin.php:208
msgid "Error: %s"
msgstr "Errore: %s"

#. translators: %s: success message
#: admin/class-yoast-network-admin.php:206
msgid "Success: %s"
msgstr "Successo: %s"

#. translators: %s expands to the ID of a site within a multisite network.
#: admin/class-yoast-network-admin.php:168
msgid "Site with ID %d not found."
msgstr "Il sito web con l'ID %d non è stato trovato."

#: admin/class-yoast-network-admin.php:159
msgid "No site has been selected to restore."
msgstr "Nessun sito web è stato selezionato per il ripristino."

#: admin/class-yoast-network-admin.php:120
msgid "You are not allowed to modify unregistered network settings."
msgstr "Non sei autorizzato a modificare le impostazioni di rete non registrate."

#: admin/class-yoast-network-admin.php:81
msgid "deleted"
msgstr "eliminato"

#: inc/class-wpseo-replace-vars.php:1475
msgid "The site's tagline"
msgstr "Tagline (motto) del sito"

#. translators: %1$s expands to the missing field name.
#: admin/menu/class-replacevar-editor.php:152
msgid "Not all required fields are given. Missing field %1$s"
msgstr "Non sono stati riempiti tutti i campi obbligatori. Manca il campo %1$s"

#: inc/class-wpseo-replace-vars.php:1489 js/dist/elementor.js:540
#: js/dist/externals-redux.js:1
msgid "Current year"
msgstr "Anno corrente"

#: inc/class-wpseo-replace-vars.php:1475 js/dist/elementor.js:540
#: js/dist/externals-redux.js:1 js/dist/new-settings.js:38
#: js/dist/new-settings.js:306
msgid "Tagline"
msgstr "Motto del sito"

#: inc/class-wpseo-replace-vars.php:1520 js/dist/elementor.js:540
#: js/dist/externals-redux.js:1
msgid "Page"
msgstr "Pagina"

#: inc/class-wpseo-replace-vars.php:1528
msgid "description (custom taxonomy)"
msgstr "descrizione (tassonomia personalizzata)"

#: inc/class-wpseo-replace-vars.php:1527
msgid "(custom taxonomy)"
msgstr "(tassonomia personalizzata)"

#: inc/class-wpseo-replace-vars.php:1526
msgid "(custom field)"
msgstr "(campo personalizzato)"

#: inc/class-wpseo-replace-vars.php:1525
msgid "Term404"
msgstr "Errore 404"

#: inc/class-wpseo-replace-vars.php:1523
msgid "Caption"
msgstr "Didascalia"

#: inc/class-wpseo-replace-vars.php:1522
msgid "Pagenumber"
msgstr "Numero di pagina"

#: inc/class-wpseo-replace-vars.php:1521
msgid "Pagetotal"
msgstr "Totale pagine"

#: inc/class-wpseo-replace-vars.php:1519
msgid "User description"
msgstr "Descrizione dell'utente"

#: inc/class-wpseo-replace-vars.php:1517 js/dist/elementor.js:540
#: js/dist/externals-redux.js:1
msgid "ID"
msgstr "ID"

#: inc/class-wpseo-replace-vars.php:1516
msgid "Modified"
msgstr "Modificato"

#: inc/class-wpseo-replace-vars.php:1515
msgid "Post type (plural)"
msgstr "Post type (plurale)"

#: inc/class-wpseo-replace-vars.php:1514
msgid "Post type (singular)"
msgstr "Post type (singolare)"

#: inc/class-wpseo-replace-vars.php:1487 js/dist/elementor.js:540
#: js/dist/externals-redux.js:1 js/dist/new-settings.js:38
msgid "Separator"
msgstr "Separatore"

#: inc/class-wpseo-replace-vars.php:1485 js/dist/elementor.js:540
#: js/dist/externals-redux.js:1
msgid "Search phrase"
msgstr "Frase di ricerca"

#: inc/class-wpseo-replace-vars.php:1484
msgid "Term title"
msgstr "Titolo del termine"

#: inc/class-wpseo-replace-vars.php:1483 js/dist/elementor.js:540
#: js/dist/externals-redux.js:1
msgid "Term description"
msgstr "Descrizione del termine"

#: inc/class-wpseo-replace-vars.php:1482 js/dist/elementor.js:540
#: js/dist/externals-redux.js:1
msgid "Tag description"
msgstr "Descrizione del tag"

#: inc/class-wpseo-replace-vars.php:1481 js/dist/elementor.js:540
#: js/dist/externals-redux.js:1
msgid "Category description"
msgstr "Descrizione della categoria"

#: inc/class-wpseo-replace-vars.php:1480 js/dist/elementor.js:540
#: js/dist/externals-redux.js:1
msgid "Primary category"
msgstr "Categoria primaria"

#: inc/class-wpseo-replace-vars.php:1479 js/dist/elementor.js:540
#: js/dist/externals-redux.js:1
msgid "Category"
msgstr "Categoria"

#: inc/class-wpseo-replace-vars.php:1478
msgid "Tag"
msgstr "Tag"

#: inc/class-wpseo-replace-vars.php:1477 js/dist/elementor.js:540
#: js/dist/externals-redux.js:1
msgid "Excerpt only"
msgstr "Solo riassunto"

#: inc/class-wpseo-replace-vars.php:1476 js/dist/elementor.js:540
#: js/dist/externals-redux.js:1
msgid "Excerpt"
msgstr "Riassunto"

#: inc/class-wpseo-replace-vars.php:1474 js/dist/elementor.js:540
#: js/dist/externals-redux.js:1
msgid "Site title"
msgstr "Titolo del sito"

#: inc/class-wpseo-replace-vars.php:1473
msgid "Archive title"
msgstr "Titolo dell'archivio"

#: inc/class-wpseo-replace-vars.php:1472 js/dist/elementor.js:540
#: js/dist/externals-redux.js:1
msgid "Parent title"
msgstr "Titolo genitore"

#: inc/class-wpseo-replace-vars.php:1470 js/dist/elementor.js:540
#: js/dist/externals-redux.js:1
msgid "Date"
msgstr "Data"

#: inc/class-wpseo-admin-bar-menu.php:608
msgid "Get Yoast SEO Premium"
msgstr "Passa a Yoast SEO Premium"

#: admin/watchers/class-slug-change-watcher.php:224
msgid "You should create a redirect to ensure your visitors do not get a 404 error when they click on the no longer working URL."
msgstr "Dovresti creare un re-indirizzamento per assicurarti che i visitatori non incorrano in un errore 404 facendo clic su un URL non più funzionante."

#: admin/watchers/class-slug-change-watcher.php:90
#: admin/watchers/class-slug-change-watcher.php:113
msgid "Search engines and other websites can still send traffic to your deleted content."
msgstr "I motori di ricerca e altri siti web possono ancora inviare il traffico all'articolo eliminato."

#: admin/watchers/class-slug-change-watcher.php:220
msgid "Make sure you don't miss out on traffic!"
msgstr "Assicurati di non perdere traffico!"

#. translators: %1$s expands to the translated name of the post type.
#. translators: %1$s expands to the translated name of the term.
#: admin/watchers/class-slug-change-watcher.php:89
#: admin/watchers/class-slug-change-watcher.php:112
msgid "You just deleted a %1$s."
msgstr "Hai appena eliminato %1$s."

#. translators: %1$s expands to the translated name of the post type.
#: admin/watchers/class-slug-change-watcher.php:67
msgid "You just trashed a %1$s."
msgstr "Hai appena spostato nel cestino una %1$s."

#. translators: %s is replaced with Yoast SEO.
#: admin/import/plugins/class-abstract-plugin-importer.php:259
msgid "The %s importer functionality uses temporary database tables. It seems your WordPress install does not have the capability to do this, please consult your hosting provider."
msgstr "La funzione %s di importazione usa tabelle temporanee di database. Sembra che la tua installazione di WordPress non sia in grado di gestirle, consulta il tuo provider."

#. translators: %s is replaced with the plugin's name.
#: admin/import/plugins/class-abstract-plugin-importer.php:132
msgid "Cleanup of %s data failed."
msgstr "La pulizia dati di %s non è andata a buon fine."

#: admin/class-bulk-editor-list-table.php:1037
msgid "Content Type"
msgstr "Tipo di contenuto"

#. translators: Hidden accessibility text.
#: admin/class-bulk-editor-list-table.php:429
msgid "Filter by content type"
msgstr "Filtra per tipo di contenuto"

#: admin/class-bulk-editor-list-table.php:411
msgid "Show All Content Types"
msgstr "Mostra tutti i tipi di contenuto"

#: inc/class-wpseo-replace-vars.php:1473
msgid "Replaced with the normal title for an archive generated by WordPress"
msgstr "Sostituisci con il titolo normale per un archivio generato da WordPress"

#: admin/views/tabs/tool/import-seo.php:126
msgid "Clean"
msgstr "Pulisci"

#: admin/views/tabs/tool/import-seo.php:117
msgid "Once you're certain your site is OK, you can clean up. This will remove all the original data."
msgstr "Una volta che sei sicuro che il tuo sito sia a posto, puoi pulire. Questo rimuoverà tutti i dati originali."

#: admin/views/tabs/tool/import-seo.php:115
msgid "Step 5: Clean up"
msgstr "Passaggio 5: Pulisci"

#: admin/views/tabs/tool/import-seo.php:95
msgid "Please check your posts and pages and see if the metadata was successfully imported."
msgstr "Controlla le tue pagine e articoli e verifica che i metadata sono stati importati con successo."

#: admin/views/tabs/tool/import-seo.php:93
msgid "Step 3: Check your data"
msgstr "Passaggio 3: Verifica i tuoi dati"

#. translators: 1: expands to Yoast SEO
#: admin/views/tabs/tool/import-seo.php:67
msgid "This will import the post metadata like SEO titles and descriptions into your %1$s metadata. It will only do this when there is no existing %1$s metadata yet. The original data will remain in place."
msgstr "Questo importerà i metadata degli articoli come i titoli SEO e le descrizioni nei metadati di %1$s. Esegui l'azione solo se non ci sono ancora i metadati di %1$s. I dati esistenti non andranno persi."

#: admin/views/tabs/tool/import-seo.php:62
msgid "Step 2: Import"
msgstr "Passaggio 2: Importa"

#: admin/views/tabs/tool/import-seo.php:57
msgid "Please make a backup of your database before starting this process."
msgstr "Fai un backup del database prima di iniziare questo processo."

#: admin/views/tabs/tool/import-seo.php:55
msgid "Step 1: Create a backup"
msgstr "Passaggio 1: Crea un backup"

#: admin/views/tabs/tool/import-seo.php:51
msgid "We've detected data from one or more SEO plugins on your site. Please follow the following steps to import that data:"
msgstr "Abbiamo trovato uno o più dati appartenenti ad altri plugin di SEO nel tuo sito. Segui i seguenti passi per importare i dati."

#: admin/views/tabs/tool/import-seo.php:39
msgid "Plugin: "
msgstr "Plugin:"

#. translators: %s expands to Yoast SEO
#: admin/views/tabs/tool/import-seo.php:22
msgid "%s did not detect any plugin data from plugins it can import from."
msgstr "%s non ha trovato nessun dato dai plugin che possa essere importato."

#: admin/statistics/class-statistics-service.php:229
msgid "Posts that should not show up in search results"
msgstr "Articoli da non mostrare nei risultati delle ricerche"

#. translators: %s is replaced with the name of the plugin we've found data
#. from.
#: admin/import/class-import-status.php:128
msgid "%s data found."
msgstr "Dati trovati da %s."

#. translators: %s is replaced with the name of the plugin we're removing data
#. from.
#: admin/import/class-import-status.php:124
msgid "%s data successfully removed."
msgstr "%s dati rimossi con successo"

#. translators: %s is replaced with the name of the plugin we're importing data
#. from.
#: admin/import/class-import-status.php:121
msgid "%s data successfully imported."
msgstr "Dati importati con successo da %s."

#. translators: %s is replaced with the name of the plugin we're trying to find
#. data from.
#: admin/import/class-import-status.php:61
msgid "%s data not found."
msgstr "Non sono stati trovati dati da %s."

#: admin/views/user-profile.php:17
msgid "this author's archives"
msgstr "archivi di questo autore"

#. translators: %s expands to "this author's archives".
#: admin/views/user-profile.php:16
msgid "Do not allow search engines to show %s in search results."
msgstr "Non consentire ai motori di ricerca di mostrare %s nei risultati di ricerca."

#: admin/class-yoast-form.php:960 admin/class-yoast-form.php:1000
#: js/dist/externals/componentsNew.js:766
msgid "On"
msgstr "On"

#. translators: Hidden accessibility text; %s expands to a feature's name.
#. translators: Hidden accessibility text; %s expands to an integration's name.
#: admin/views/tabs/network/features.php:62
#: admin/views/tabs/network/integrations.php:50
msgid "Help on: %s"
msgstr "Aiuto su: %s"

#: admin/class-yoast-form.php:961 admin/class-yoast-form.php:1001
#: admin/views/class-yoast-feature-toggles.php:160
#: js/dist/externals/componentsNew.js:766
msgid "Off"
msgstr "Off"

#: admin/views/class-yoast-feature-toggles.php:140
msgid "Read why XML Sitemaps are important for your site."
msgstr "Leggi perché gli le sitemap XML sono importanti per il tuo sito."

#. translators: %s: Yoast SEO
#: admin/views/class-yoast-feature-toggles.php:139
msgid "Enable the XML sitemaps that %s generates."
msgstr "Abilita le sitemap XML generate da %s."

#: admin/views/class-yoast-feature-toggles.php:70
msgid "See the XML sitemap."
msgstr "Vedi la mappa XML del sito."

#. translators: %1$s expands to Yoast SEO
#: admin/views/tabs/dashboard/dashboard.php:19
msgid "See who contributed to %1$s."
msgstr "Guarda chi ha contribuito a %1$s."

#. translators: %1$s expands to the post type name.
#: admin/metabox/class-metabox.php:189
msgid "Should search engines follow links on this %1$s?"
msgstr "I motori di ricerca dovrebbero seguire i link per questo %1$s?"

#. translators: %1$s expands to Yes or No,  %2$s expands to the post type name.
#: admin/metabox/class-metabox.php:184
msgid "Default for %2$s, currently: %1$s"
msgstr "Predefinito per %2$s, attualmente: %1$s"

#. translators: %s expands to the post type name.
#: admin/metabox/class-metabox.php:179
msgid "Allow search engines to show this %s in search results?"
msgstr "Consenti ai motori di ricerca di mostrare %s nei risultati delle ricerche?"

#. translators: %s expands to an indexable object's name, like a post type or
#. taxonomy
#: admin/class-yoast-form.php:971
msgid "Show %s in search results?"
msgstr "Vuoi mostrare %s nei risultati di ricerca?"

#. translators: %1$s expands to Yoast SEO.
#: admin/class-plugin-conflict.php:82
msgid "Toggle %1$s's XML Sitemap"
msgstr "Attiva la sitemap XML di %1$s"

#. translators: %s: 'Semrush'
#. translators: %s: Algolia.
#: admin/views/class-yoast-integration-toggles.php:67
#: admin/views/class-yoast-integration-toggles.php:78
msgid "%s integration"
msgstr "Integrazione con %s"

#: admin/views/class-yoast-feature-toggles.php:111
msgid "Find out how the text link counter can enhance your SEO."
msgstr "Scopri come il contatore di link di testo può migliorare la tua SEO."

#: admin/views/class-yoast-feature-toggles.php:110
msgid "The text link counter helps you improve your site structure."
msgstr "Il contatore di link nel testo ti aiuta a migliorare la struttura del tuo sito."

#: admin/views/class-yoast-feature-toggles.php:103
msgid "Find out how cornerstone content can help you improve your site structure."
msgstr "Scopri come i contenuti cornerstone possono aiutarti a migliorare la struttura del tuo sito."

#: admin/views/class-yoast-feature-toggles.php:102
msgid "The cornerstone content feature lets you to mark and filter cornerstone content on your website."
msgstr "La funzione del contenuto cornerstone ti consente di contrassegnare e filtrare i contenuti cornerstone sul tuo sito web."

#: admin/views/class-yoast-feature-toggles.php:86
msgid "Discover why readability is important for SEO."
msgstr "Scopri perché la leggibilità è importante per la strategia SEO."

#: admin/views/class-yoast-feature-toggles.php:85 js/dist/new-settings.js:312
msgid "The readability analysis offers suggestions to improve the structure and style of your text."
msgstr "L'analisi di leggibilità offre suggerimenti per migliorare la struttura e lo stile del testo."

#: admin/views/class-yoast-feature-toggles.php:78
msgid "Learn how the SEO analysis can help you rank."
msgstr "Scopri come l'analisi SEO può aiutare il tuo posizionamento."

#: admin/views/class-yoast-feature-toggles.php:77
msgid "The SEO analysis offers suggestions to improve the SEO of your text."
msgstr "L'analisi SEO offre suggerimenti per migliorare la strategia SEO del tuo testo."

#: admin/views/class-yoast-feature-toggles.php:75
#: js/dist/externals-components.js:264 js/dist/new-settings.js:38
#: js/dist/new-settings.js:312
msgid "SEO analysis"
msgstr "Analisi SEO"

#. Author URI of the plugin
#: wp-seo.php
msgid "https://yoa.st/1uk"
msgstr "https://yoa.st/1uk"

#. Plugin URI of the plugin
#: wp-seo.php
msgid "https://yoa.st/1uj"
msgstr "https://yoa.st/1uj"

#. translators: %1$s resolves to Yoast.com
#: admin/class-yoast-dashboard-widget.php:130
msgid "Latest blog posts on %1$s"
msgstr "Post recenti sul blog %1$s"

#: src/helpers/first-time-configuration-notice-helper.php:64
msgid "First-time SEO configuration"
msgstr "Configurazione SEO iniziale"

#. translators: %s expands to ".htaccess".
#: admin/views/tool-file-editor.php:186
msgid "%s file"
msgstr "File %s"

#. translators: %s expands to robots.txt.
#. translators: %s expands to ".htaccess".
#: admin/views/tool-file-editor.php:174 admin/views/tool-file-editor.php:224
msgid "Save changes to %s"
msgstr "Salva le modifiche al %s"

#. translators: %s expands to robots.txt.
#. translators: %s expands to ".htaccess".
#: admin/views/tool-file-editor.php:165 admin/views/tool-file-editor.php:215
msgid "Edit the content of your %s:"
msgstr "Modifica il contenuto del tuo %s:"

#. translators: %s expands to robots.txt.
#: admin/views/tool-file-editor.php:125
msgid "Create %s file"
msgstr "Crea file %s"

#. translators: %s expands to robots.txt.
#: admin/views/tool-file-editor.php:65
msgid "Updated %s"
msgstr "%s aggiornato"

#. translators: %s expands to robots.txt.
#. translators: %s expands to ".htaccess".
#: admin/views/tool-file-editor.php:49 admin/views/tool-file-editor.php:76
msgid "You cannot edit the %s file."
msgstr "Non è possibile modificare il file %s."

#. translators: %s expands to robots.txt.
#: admin/views/tool-file-editor.php:28
msgid "You cannot create a %s file."
msgstr "Non è possibile creare un file %s."

#. translators: Hidden accessibility text; %1$s expands to the dependency name
#: admin/class-suggested-plugins.php:137
msgid "More information about %1$s"
msgstr "Ulteriori informazioni su %1$s"

#: src/integrations/admin/old-configuration-integration.php:38
msgid "Old Configuration Wizard"
msgstr "Configurazione guidata"

#. translators: %1$s expands to the posttype label, %2$s expands anchor to blog
#. post about cornerstone content, %3$s expands to </a>
#: admin/filters/class-cornerstone-filter.php:104
msgid "Mark the most important %1$s as 'cornerstone content' to improve your site structure. %2$sLearn more about cornerstone content%3$s."
msgstr "Contrassegna gli (le) %1$s più importanti come 'cornerstone content' (contenuti centrali) per migliorare la struttura del tuo sito. %2$sLeggi di più sui contenuti cornerstone%3$s."

#. translators: Hidden accessibility text.
#: admin/class-admin-utils.php:79 admin/class-premium-popup.php:83
#: admin/class-premium-upsell-admin-block.php:102
#: admin/class-yoast-form.php:935
#: admin/watchers/class-slug-change-watcher.php:232
#: src/integrations/admin/workouts-integration.php:215
#: src/integrations/admin/workouts-integration.php:245
#: src/presenters/admin/help-link-presenter.php:76 js/dist/academy.js:9
#: js/dist/block-editor.js:19 js/dist/block-editor.js:23
#: js/dist/block-editor.js:25 js/dist/block-editor.js:541
#: js/dist/block-editor.js:542 js/dist/classic-editor.js:4
#: js/dist/classic-editor.js:8 js/dist/classic-editor.js:10
#: js/dist/elementor.js:4 js/dist/elementor.js:8 js/dist/elementor.js:10
#: js/dist/externals-components.js:21 js/dist/externals-components.js:191
#: js/dist/externals-components.js:195 js/dist/externals-components.js:197
#: js/dist/externals/componentsNew.js:1044 js/dist/externals/helpers.js:6
#: js/dist/externals/relatedKeyphraseSuggestions.js:3
#: js/dist/externals/relatedKeyphraseSuggestions.js:7 js/dist/general-page.js:6
#: js/dist/general-page.js:10 js/dist/general-page.js:12
#: js/dist/general-page.js:36 js/dist/integrations-page.js:3
#: js/dist/integrations-page.js:4 js/dist/integrations-page.js:5
#: js/dist/integrations-page.js:6 js/dist/integrations-page.js:7
#: js/dist/integrations-page.js:8 js/dist/integrations-page.js:13
#: js/dist/integrations-page.js:18 js/dist/integrations-page.js:19
#: js/dist/integrations-page.js:20 js/dist/integrations-page.js:40
#: js/dist/integrations-page.js:44 js/dist/integrations-page.js:46
#: js/dist/introductions.js:3 js/dist/introductions.js:4
#: js/dist/new-settings.js:6 js/dist/new-settings.js:10
#: js/dist/new-settings.js:12 js/dist/plans.js:2 js/dist/post-edit.js:4
#: js/dist/post-edit.js:8 js/dist/post-edit.js:10 js/dist/support.js:6
#: js/dist/support.js:10 js/dist/support.js:12 js/dist/support.js:24
#: js/dist/term-edit.js:4 js/dist/term-edit.js:8 js/dist/term-edit.js:10
msgid "(Opens in a new browser tab)"
msgstr "(Si apre in una nuova scheda del browser)"

#. translators: %1$s expands to an opening strong tag, %2$s expands to a
#. closing strong tag
#: admin/statistics/class-statistics-service.php:210
msgid "Posts %1$swithout%2$s a focus keyphrase"
msgstr "Articoli %1$ssenza%2$s frase chiave"

#: admin/statistics/class-statistics-service.php:77
msgid "Hey, your SEO is doing pretty well! Check out the stats:"
msgstr "Ehi, la tua SEO sta andando abbastanza bene! Guarda le statistiche:"

#: admin/statistics/class-statistics-service.php:73
msgid "You don't have any published posts, your SEO scores will appear here once you make your first post!"
msgstr "Non hai pubblicato nessun post, i tuoi punteggi SEO appariranno qui appena scriverari il tuo primo post!"

#: admin/class-yoast-dashboard-widget.php:133
msgid "Read more like this on our SEO blog"
msgstr "Leggi più articoli di questo tipo nel nostro blog SEO."

#. translators: %s expands to the readability score
#: inc/class-wpseo-rank.php:207 inc/class-wpseo-rank.php:212
#: inc/class-wpseo-rank.php:217 inc/class-wpseo-rank.php:222
msgid "Readability: %s"
msgstr "Leggibilità: %s"

#. translators: %1$s expands to Yoast
#: src/presenters/admin/sidebar-presenter.php:31
msgid "%1$s recommendations for you"
msgstr "Consigli per te da %1$s"

#: admin/class-meta-columns.php:302
msgid "All Readability Scores"
msgstr "Tutti i punteggi di leggibilità"

#. translators: Hidden accessibility text.
#: admin/class-meta-columns.php:298
msgid "Filter by Readability Score"
msgstr "Filtra per punteggio di leggibilità"

#. translators: %1$s expands to the request method
#: admin/class-remote-request.php:97
msgid "Request method %1$s is not valid."
msgstr "Il metodo di richiesta %1$s non è valido."

#: admin/views/class-yoast-feature-toggles.php:108 js/dist/new-settings.js:38
#: js/dist/new-settings.js:312
msgid "Text link counter"
msgstr "Contatore di link nei testi"

#. translators: %s expands to Yoast
#: admin/class-yoast-columns.php:64
msgid "%s Columns"
msgstr "Colonne %s"

#: admin/class-meta-columns.php:122 admin/class-meta-columns.php:124
#: admin/taxonomy/class-taxonomy-columns.php:92
#: admin/taxonomy/class-taxonomy-columns.php:93
msgid "Readability score"
msgstr "Punteggio di leggibilità"

#: admin/filters/class-cornerstone-filter.php:87
#: admin/views/class-yoast-feature-toggles.php:100
#: js/dist/externals-components.js:20 js/dist/new-settings.js:38
#: js/dist/new-settings.js:312
msgid "Cornerstone content"
msgstr "Contenuto Cornerstone (contenuto centrale)"

#: admin/class-yoast-form.php:149 admin/class-yoast-form.php:154
#: js/dist/general-page.js:48 js/dist/new-settings.js:23
msgid "Save changes"
msgstr "Salva le modifiche"

#: admin/class-premium-popup.php:89 js/dist/block-editor.js:285
#: js/dist/block-editor.js:535 js/dist/classic-editor.js:270
#: js/dist/classic-editor.js:520 js/dist/elementor.js:91
#: js/dist/elementor.js:540 js/dist/externals-components.js:243
#: js/dist/externals-components.js:245
msgid "1 year free support and updates included!"
msgstr "1 anno di aggiornamenti e supporto personalizzato inclusi!"

#. translators: %2$s expands to 'RS Head Cleaner' plugin name of possibly
#. conflicting plugin with regard to differentiating output between search
#. engines and normal users.
#: admin/class-plugin-conflict.php:87
msgid "The plugin %2$s changes your site's output and in doing that differentiates between search engines and normal users, a process that's called cloaking. We highly recommend that you disable it."
msgstr "Il plugin %2$s modifica l'aspetto del tuo sito differenziando il contenuto per i motori di ricerca da quello per gli utenti \"normali\". Questo è un processo chiamato cloaking, che potrebbe portarti a brutte penalizzazioni. Ti suggeriamo di disabilitarlo."

#: admin/class-premium-upsell-admin-block.php:92
msgid "No ads!"
msgstr "Nessuna pubblicità!"

#. translators: %s expands to Yoast SEO Premium
#: src/presenters/admin/sidebar-presenter.php:85
msgid "Get %1$s"
msgstr "Passa a %1$s"

#: admin/class-admin.php:355
msgid "Scroll to see the table content."
msgstr "Scorri per vedere il contenuto della tabella."

#: admin/views/partial-notifications-warnings.php:22 js/dist/general-page.js:36
msgid "No new notifications."
msgstr "Nessuna nuova notifica."

#: admin/class-bulk-editor-list-table.php:922
msgid "Save all"
msgstr "Salva tutto"

#: admin/class-bulk-editor-list-table.php:921
msgid "Save"
msgstr "Salva"

#. translators: 1: Author name; 2: Site name.
#: inc/options/class-wpseo-option-titles.php:272
msgid "%1$s, Author at %2$s"
msgstr "%1$s, Autore presso %2$s"

#: inc/class-wpseo-replace-vars.php:1518 js/dist/general-page.js:48
msgid "Name"
msgstr "Nome"

#: admin/views/tool-import-export.php:89
msgid "Export settings"
msgstr "Esporta le impostazioni"

#: admin/class-product-upsell-notice.php:181
msgid "Please don't show me this notification anymore"
msgstr "Non mostrarmi più questa notifica"

#. translators: %1$s is a link start tag to the bugreport guidelines on the
#. Yoast help center, %2$s is the link closing tag.
#: admin/class-product-upsell-notice.php:174
msgid "If you are experiencing issues, %1$splease file a bug report%2$s and we'll do our best to help you out."
msgstr "Se stai riscontrando problemi, %1$sinviaci un bug report%2$s e faremo del nostro meglio per aiutarti."

#. translators: %1$s expands to Yoast SEO, %2$s is a link start tag to the
#. plugin page on WordPress.org, %3$s is the link closing tag.
#: admin/class-product-upsell-notice.php:166
msgid "We've noticed you've been using %1$s for some time now; we hope you love it! We'd be thrilled if you could %2$sgive us a 5 stars rating on WordPress.org%3$s!"
msgstr "Abbiamo notato che stai utilizzando %1$s da un po' di tempo; ci auguriamo che ti stia piacendo! Saremmo entusiasti se tu volessi %2$sdarci un punteggio di 5 stelle su WordPress.org%3$s!"

#. translators: %1$s: '%%term_title%%' variable used in titles and meta's
#. template that's not compatible with the given template, %2$s: expands to
#. 'HelpScout beacon'
#: admin/class-admin.php:349
msgid "Warning: the variable %1$s cannot be used in this template. See the %2$s for more info."
msgstr "Attenzione: la variabile %1$s non può essere utilizzata in questo template. Vai su %2$s per maggiori informazioni."

#. translators: %1$s expands anchor to premium plugin page, %2$s expands to
#. </a>
#: admin/class-product-upsell-notice.php:149
msgid "By the way, did you know we also have a %1$sPremium plugin%2$s? It offers advanced features, like a redirect manager and support for multiple keyphrases. It also comes with 24/7 personal support."
msgstr "A proposito, sapevi che abbiamo anche un %1$sPlugin Premium%2$s? Fornisce funzionalità più avanzate, quali un gestore di redirect ed il supporto per frasi chiave multiple. Inoltre dispone di una assistenza personalizzata 24/7."

#: admin/class-bulk-editor-list-table.php:829
msgid "(no title)"
msgstr "(senza titolo)"

#. translators: 1: Yoast SEO
#: admin/views/class-yoast-feature-toggles.php:150
msgid "The %1$s admin bar menu contains useful links to third-party tools for analyzing pages and makes it easy to see if you have new notifications."
msgstr "La barra di amministrazione di %1$s contiene dei link utili a strumenti di terze parti per analizzare le pagine e rendere semplice verificare se si hanno nuove notifiche."

#: admin/views/class-yoast-feature-toggles.php:147 js/dist/new-settings.js:38
#: js/dist/new-settings.js:312 js/dist/new-settings.js:314
msgid "Admin bar menu"
msgstr "Menu della barra di amministrazione"

#: admin/pages/network.php:19 admin/views/tabs/network/features.php:22
msgid "Features"
msgstr "Funzionalità"

#: admin/metabox/class-metabox.php:175 js/dist/externals/analysis.js:107
#: js/dist/externals/analysis.js:207
#: js/dist/externals/replacementVariableEditor.js:27
#: js/dist/externals/searchMetadataPreviews.js:62 js/dist/new-settings.js:31
#: js/dist/new-settings.js:35 js/dist/new-settings.js:38
#: js/dist/new-settings.js:42 js/dist/new-settings.js:68
#: js/dist/new-settings.js:82 js/dist/new-settings.js:112
#: js/dist/new-settings.js:141 js/dist/new-settings.js:206
#: js/dist/new-settings.js:223 js/dist/new-settings.js:232
#: js/dist/new-settings.js:254
msgid "SEO title"
msgstr "Titolo SEO"

#: inc/options/class-wpseo-option-titles.php:989
msgid "Greater than sign"
msgstr "Simbolo di maggiore"

#: inc/options/class-wpseo-option-titles.php:985
msgid "Less than sign"
msgstr "Simboli di minore"

#: inc/options/class-wpseo-option-titles.php:981
msgid "Right angle quotation mark"
msgstr "Virgoletta bassa destra"

#: inc/options/class-wpseo-option-titles.php:977
msgid "Left angle quotation mark"
msgstr "Virgoletta bassa sinistra"

#: inc/options/class-wpseo-option-titles.php:973
msgid "Small tilde"
msgstr "Tilde piccola"

#: inc/options/class-wpseo-option-titles.php:969
msgid "Vertical bar"
msgstr "Barra verticale"

#: inc/options/class-wpseo-option-titles.php:965
msgid "Low asterisk"
msgstr "Asterisco basso"

#: inc/options/class-wpseo-option-titles.php:961
msgid "Asterisk"
msgstr "Asterisco"

#: inc/options/class-wpseo-option-titles.php:957
msgid "Bullet"
msgstr "Punto elenco"

#: inc/options/class-wpseo-option-titles.php:953
msgid "Middle dot"
msgstr "punto medio"

#: inc/options/class-wpseo-option-titles.php:945
msgid "Em dash"
msgstr "trattino em"

#: inc/options/class-wpseo-option-titles.php:941
msgid "En dash"
msgstr "trattino en"

#: inc/options/class-wpseo-option-titles.php:937
msgid "Dash"
msgstr "Trattino"

#: admin/metabox/class-metabox.php:186 admin/metabox/class-metabox.php:191
#: js/dist/block-editor.js:285 js/dist/classic-editor.js:270
#: js/dist/elementor.js:393
msgid "No"
msgstr "No"

#: admin/metabox/class-metabox.php:185 admin/metabox/class-metabox.php:190
#: js/dist/block-editor.js:285 js/dist/classic-editor.js:270
#: js/dist/elementor.js:393
msgid "Yes"
msgstr "Si"

#: admin/views/tool-bulk-editor.php:63
msgid "Posts list"
msgstr "Elenco articoli"

#: admin/views/tool-bulk-editor.php:62
msgid "Posts list navigation"
msgstr "Navigazione elenco articoli"

#: admin/views/tool-bulk-editor.php:61
msgid "Filter posts list"
msgstr "Filtro elenco articoli"

#. translators: Hidden accessibility text; %s: post title.
#: admin/class-bulk-editor-list-table.php:843
msgid "Edit &#8220;%s&#8221;"
msgstr "Modifica &#8220;%s&#8221;"

#: src/integrations/admin/menu-badge-integration.php:35 js/dist/academy.js:8
msgid "Premium"
msgstr "Premium"

#: admin/class-admin.php:266
msgid "Get Premium"
msgstr "Passa a Premium"

#: admin/views/partial-notifications-warnings.php:20
#: inc/class-wpseo-admin-bar-menu.php:400 js/dist/general-page.js:36
msgid "Notifications"
msgstr "Notifiche"

#: admin/views/user-profile.php:50
#: src/user-meta/framework/custom-meta/keyword-analysis-disable.php:115
msgid "Removes the focus keyphrase section from the metabox and disables all SEO-related suggestions."
msgstr "Rimuovi la scheda delle frasi chiave dal meta box e disabilita tutti i suggerimenti relativi alla SEO."

#: admin/views/user-profile.php:47
#: src/user-meta/framework/custom-meta/keyword-analysis-disable.php:109
msgid "Disable SEO analysis"
msgstr "Disabilita l'analisi SEO"

#: admin/views/js-templates-primary-term.php:28
msgid "Make primary"
msgstr "Rendi primario"

#. translators: Hidden accessibility text; %s: number of notifications.
#: admin/menu/class-admin-menu.php:118 inc/class-wpseo-admin-bar-menu.php:862
#: js/dist/general-page.js:56
msgid "%s notification"
msgid_plural "%s notifications"
msgstr[0] "%s notifica"
msgstr[1] "%s notifiche"

#: admin/views/user-profile.php:59
#: src/user-meta/framework/custom-meta/content-analysis-disable.php:109
msgid "Disable readability analysis"
msgstr "Disabilita l'analisi di leggibilità"

#: admin/views/user-profile.php:62
#: src/user-meta/framework/custom-meta/content-analysis-disable.php:115
msgid "Removes the readability analysis section from the metabox and disables all readability-related suggestions."
msgstr "Rimuovi la scheda dell'analisi di leggibilità dal meta box e disabilita tutti i suggerimenti sulla leggibilità."

#: admin/views/class-yoast-feature-toggles.php:83
#: js/dist/externals-components.js:219 js/dist/new-settings.js:38
#: js/dist/new-settings.js:312
msgid "Readability analysis"
msgstr "Analisi di leggibilità"

#: admin/statistics/class-statistics-service.php:217
#: inc/class-wpseo-rank.php:140 inc/class-wpseo-rank.php:176
#: inc/class-wpseo-rank.php:208 inc/class-wpseo-rank.php:240
#: js/dist/block-editor.js:540 js/dist/editor-modules.js:315
#: js/dist/elementor.js:94 js/dist/externals-components.js:188
#: js/dist/externals/analysis.js:15 js/dist/externals/dashboardFrontend.js:4
#: js/dist/frontend-inspector-resources.js:1 js/dist/post-edit.js:25
#: js/dist/term-edit.js:1
msgid "Needs improvement"
msgstr "Da migliorare"

#: admin/metabox/class-metabox-section-readability.php:30
#: inc/class-wpseo-admin-bar-menu.php:256
msgid "Readability"
msgstr "Leggibilità"

#: admin/views/partial-notifications-errors.php:22 js/dist/general-page.js:36
msgid "Good job! We could detect no serious SEO problems."
msgstr "Buon lavoro! Non siamo in grado di individuare nessun problema SEO."

#: admin/views/partial-notifications-errors.php:21 js/dist/general-page.js:36
msgid "We have detected the following issues that affect the SEO of your site."
msgstr "Abbiamo individuato i seguenti problemi che riguardano la SEO del tuo sito."

#: admin/views/partial-notifications-errors.php:20
#: js/dist/editor-modules.js:181 js/dist/externals-components.js:188
#: js/dist/externals/analysisReport.js:39 js/dist/general-page.js:36
msgid "Problems"
msgstr "Problemi"

#: inc/class-wpseo-rank.php:138 js/dist/block-editor.js:540
#: js/dist/editor-modules.js:315 js/dist/elementor.js:94
#: js/dist/externals-components.js:188
#: js/dist/frontend-inspector-resources.js:1 js/dist/post-edit.js:25
#: js/dist/term-edit.js:1
msgid "Not available"
msgstr "Non disponibile"

#. translators: Hidden accessibility text.
#: admin/class-meta-columns.php:269
msgid "Filter by SEO Score"
msgstr "Filtra per punteggio SEO"

#. translators: Hidden accessibility text.
#: admin/class-meta-columns.php:181
msgid "Meta description not set."
msgstr "Meta descrizione non impostata."

#: admin/menu/class-admin-menu.php:56 js/dist/general-page.js:55
msgid "Dashboard"
msgstr "Bacheca"

#. translators: %1$s is a link start tag to the permalink settings page, %2$s
#. is the link closing tag.
#: src/services/health-check/postname-permalink-reports.php:71
msgid "You can fix this on the %1$sPermalink settings page%2$s."
msgstr "Puoi correggerlo nella %1$sPagina delle impostazioni dei permalink%2$s."

#: inc/class-wpseo-replace-vars.php:1480
msgid "Replaced with the primary category of the post/page"
msgstr "Sostituito con la categoria principale nell'articolo/pagina"

#. translators: %1$s expands to Yoast SEO
#: admin/class-bulk-title-editor-list-table.php:50
msgid "New %1$s Title"
msgstr "Nuovo %1$s Titolo"

#. translators: %1$s expands to Yoast SEO
#: admin/class-bulk-title-editor-list-table.php:48
msgid "Existing %1$s Title"
msgstr "Titolo %1$s Esistente"

#: inc/sitemaps/class-sitemaps-cache-validator.php:301
msgid "Expected an integer as input."
msgstr "L'input dev'essere un intero."

#: inc/sitemaps/class-sitemaps-cache-validator.php:111
msgid "Trying to build the sitemap cache key, but the postfix and prefix combination leaves too little room to do this. You are probably requesting a page that is way out of the expected range."
msgstr "Sto provando a costruire una chiave sicura della cache della sitemap, ma la combinazione di suffisso e prefisso lascia poco spazio di azione. Probabilmente stai cercando di elaborare una pagina con molti caratteri."

#: admin/views/redirects.php:32
#: src/integrations/admin/redirects-page-integration.php:48
msgid "Redirects"
msgstr "Redirect"

#: src/integrations/admin/crawl-settings-integration.php:242
#: js/dist/externals/relatedKeyphraseSuggestions.js:1
msgid "Remove"
msgstr "Rimuovi"

#: src/integrations/admin/crawl-settings-integration.php:241
msgid "Keep"
msgstr "Mantieni"

#. translators: %s is the taxonomy title. This will be shown to screenreaders
#: admin/views/js-templates-primary-term.php:38
msgid "Primary %s"
msgstr "%s Primario"

#: admin/views/js-templates-primary-term.php:32
msgid "Primary"
msgstr "Primario"

#. translators: Hidden accessibility text; %1$s expands to the term title, %2$s
#. to the taxonomy title.
#: admin/views/js-templates-primary-term.php:18
msgid "Make %1$s primary %2$s"
msgstr "Rendi %1$s primario %2$s"

#: admin/pages/network.php:20 admin/views/tabs/network/integrations.php:22
#: src/integrations/admin/integrations-page.php:131
#: js/dist/integrations-page.js:63
msgid "Integrations"
msgstr "Integrazioni"

#: admin/taxonomy/class-taxonomy-columns.php:170
msgid "Term is set to noindex."
msgstr "Il termine è impostato su noidex."

#: src/integrations/admin/crawl-settings-integration.php:195
#: src/presenters/admin/light-switch-presenter.php:120
msgid "Enabled"
msgstr "Abilitato"

#: src/integrations/admin/crawl-settings-integration.php:194
#: src/presenters/admin/light-switch-presenter.php:120
#: js/dist/externals/dashboardFrontend.js:4
msgid "Disabled"
msgstr "Disabilitato"

#. translators: %s: wp_title() function.
#: inc/class-wpseo-replace-vars.php:1462
msgid "The separator defined in your theme's %s tag."
msgstr "Il separatore definito nel tag %s del tuo tema."

#: inc/class-wpseo-rank.php:139
msgid "No index"
msgstr "Noindex"

#: admin/class-meta-columns.php:114 admin/class-meta-columns.php:116
#: admin/taxonomy/class-taxonomy-columns.php:87
#: admin/taxonomy/class-taxonomy-columns.php:88
#: inc/class-wpseo-admin-bar-menu.php:244
#: js/dist/externals/dashboardFrontend.js:4
msgid "SEO score"
msgstr "Punteggio SEO"

#. Author of the plugin
#: wp-seo.php
msgid "Team Yoast"
msgstr "Team Yoast"

#. Description of the plugin
#: wp-seo.php
msgid "The first true all-in-one SEO solution for WordPress, including on-page content analysis, XML sitemaps and much more."
msgstr "La prima vera soluzione SEO tutto-in-uno per WordPress, compresa l'analisi dei contenuti su ogni pagina, sitemap XML e molto altro."

#. Plugin Name of the plugin
#: wp-seo.php admin/capabilities/class-capability-manager-integration.php:74
#: src/presenters/meta-description-presenter.php:36 js/dist/block-editor.js:609
msgid "Yoast SEO"
msgstr "Yoast SEO"

#. translators: %1$s expands to Yoast SEO, %2$s / %3$s: links to the
#. installation manual in the Readme for the Yoast SEO code repository on
#. GitHub
#: wp-seo-main.php:524
msgid "The %1$s plugin installation is incomplete. Please refer to %2$sinstallation instructions%3$s."
msgstr "L'installazione del plugin %1$s è incompleta. Consulta le %2$sistruzioni per l'installazione%3$s."

#: wp-seo-main.php:500
msgid "The Standard PHP Library (SPL) extension seem to be unavailable. Please ask your web host to enable it."
msgstr "L'estensione Standard PHP Library (SPL) non pare disponibile. Richiedi al tuo host di abilitarla."

#: inc/class-wpseo-admin-bar-menu.php:636
#: inc/class-wpseo-admin-bar-menu.php:684
msgid "SEO Settings"
msgstr "Impostazioni SEO"

#: inc/class-wpseo-admin-bar-menu.php:454
msgid "Google Page Speed Test"
msgstr "Test Google Page Speed"

#: inc/class-wpseo-admin-bar-menu.php:449
msgid "Facebook Debugger"
msgstr "Debugger Facebook"

#: inc/class-wpseo-admin-bar-menu.php:430
msgid "Analyze this page"
msgstr "Analizza questa pagina"

#. translators: %s expands to the name of a post type (plural).
#: inc/class-upgrade.php:1555 inc/options/class-wpseo-option-titles.php:309
msgid "%s Archive"
msgstr "Archivi %s"

#. translators: %s expands to the search phrase.
#: inc/options/class-wpseo-option-titles.php:274
msgid "You searched for %s"
msgstr "Hai cercato %s"

#. translators: 1: link to post; 2: link to blog.
#: inc/options/class-wpseo-option-titles.php:277
msgid "The post %1$s appeared first on %2$s."
msgstr "L'articolo %1$s proviene da %2$s."

#: inc/options/class-wpseo-option-ms.php:243
msgid "No numeric value was received."
msgstr "Nessun valore numerico inserito."

#. translators: %s is the ID number of a blog.
#: inc/options/class-wpseo-option-ms.php:231
msgid "This must be an existing blog. Blog %s does not exist or has been marked as deleted."
msgstr "Questo deve essere un blog esistente. Il blog %s non esiste o è stato segnato come cancellato."

#: inc/options/class-wpseo-option-ms.php:227
#: inc/options/class-wpseo-option-ms.php:243
msgid "The default blog setting must be the numeric blog id of the blog you want to use as default."
msgstr "L'impostazione di default del blog deve essere l'id numerico del blog che vuoi usare come default."

#. translators: %1$s expands to the option name and %2$sexpands to Yoast SEO
#: inc/options/class-wpseo-option-ms.php:208
msgid "%1$s is not a valid choice for who should be allowed access to the %2$s settings. Value reset to the default."
msgstr "%1$snon è una scelta valida per chi dovrebbe essere autorizzato ad accedere alle impostazioni di %2$s. Sono stati ripristinati i valori predefiniti."

#. translators: %s expands to a taxonomy slug.
#: inc/options/class-wpseo-option-titles.php:583
msgid "Please select a valid post type for taxonomy \"%s\""
msgstr "Seleziona un tipo di contenuto valido per la tassonomia \"%s\""

#. translators: %s expands to a post type.
#: inc/options/class-wpseo-option-titles.php:545
msgid "Please select a valid taxonomy for post type \"%s\""
msgstr "Seleziona una tassonomia valida per i contenuti di tipo \"%s\""

#: inc/options/class-wpseo-option-titles.php:282
msgid "You searched for"
msgstr "Hai cercato"

#: inc/options/class-wpseo-option-titles.php:281
msgid "Home"
msgstr "Home"

#: inc/options/class-wpseo-option-titles.php:280
msgid "Archives for"
msgstr "Archivi per"

#: inc/options/class-wpseo-option-titles.php:279
msgid "Error 404: Page not found"
msgstr "Errore 404: Pagina non trovata"

#: admin/statistics/class-statistics-service.php:227
#: inc/class-wpseo-rank.php:142 inc/class-wpseo-rank.php:186
#: inc/class-wpseo-rank.php:218 inc/class-wpseo-rank.php:250
#: js/dist/block-editor.js:540 js/dist/editor-modules.js:315
#: js/dist/elementor.js:94 js/dist/externals-components.js:188
#: js/dist/externals/analysis.js:15 js/dist/externals/dashboardFrontend.js:4
#: js/dist/frontend-inspector-resources.js:1 js/dist/post-edit.js:25
#: js/dist/term-edit.js:1
msgid "Good"
msgstr "Buona"

#: inc/class-wpseo-replace-vars.php:1528
msgid "Replaced with a custom taxonomies description"
msgstr "Sostituito con la descrizione di una tassonomia custom"

#: inc/class-wpseo-replace-vars.php:1527
msgid "Replaced with a posts custom taxonomies, comma separated."
msgstr "Sostituito con una tassonomia personalizzata per gli articoli, separata da virgole."

#: inc/class-wpseo-replace-vars.php:1526
msgid "Replaced with a posts custom field value"
msgstr "Sostituita con un valore di un campo personalizzato"

#: inc/class-wpseo-replace-vars.php:1525
msgid "Replaced with the slug which caused the 404"
msgstr "Sostituito con lo slug che ha causato l'errore 404"

#: inc/class-wpseo-replace-vars.php:1524
msgid "Replaced with the posts focus keyphrase"
msgstr "Sostituito dalla frase chiave principale dell'articolo"

#: inc/class-wpseo-replace-vars.php:1523
msgid "Attachment caption"
msgstr "Didascalia Allegato"

#: inc/class-wpseo-replace-vars.php:1522
msgid "Replaced with the current page number"
msgstr "Sostituito con il numero di pagina corrente"

#: inc/class-wpseo-replace-vars.php:1521
msgid "Replaced with the current page total"
msgstr "Sostituito con il numero di pagine totale"

#: inc/class-wpseo-replace-vars.php:1520
msgid "Replaced with the current page number with context (i.e. page 2 of 4)"
msgstr "Sostituito con il numero di pagina corrente e relativo contesto(es. pagina 2 di 4)"

#: inc/class-wpseo-replace-vars.php:1489
msgid "Replaced with the current year"
msgstr "Sostituito con l'anno in corso"

#: inc/class-wpseo-replace-vars.php:1490
msgid "Replaced with the current month"
msgstr "Sostituito con il mese corrente"

#: inc/class-wpseo-replace-vars.php:1491
msgid "Replaced with the current day"
msgstr "Sostituito con la giornata in corso"

#: inc/class-wpseo-replace-vars.php:1488
msgid "Replaced with the current date"
msgstr "Sostituito con la data corrente"

#: inc/class-wpseo-replace-vars.php:1519
msgid "Replaced with the post/page author's 'Biographical Info'"
msgstr "Rimpiazzato con \"Informazioni biografiche\" dell'autore del post/pagina."

#: inc/class-wpseo-replace-vars.php:1518
msgid "Replaced with the post/page author's 'nicename'"
msgstr "Sostituito con il 'nicename' dell'autore del post/pagina"

#: inc/class-wpseo-replace-vars.php:1517
msgid "Replaced with the post/page ID"
msgstr "Sostituito con l'ID del post/pagina"

#: inc/class-wpseo-replace-vars.php:1516
msgid "Replaced with the post/page modified time"
msgstr "Sostituito con l'ora di modifica del post/pagina "

#: inc/class-wpseo-replace-vars.php:1515
msgid "Replaced with the content type plural label"
msgstr "Sostituito con l'etichetta plurima per il tipo di articolo"

#: inc/class-wpseo-replace-vars.php:1514
msgid "Replaced with the content type single label"
msgstr "Sostituito con l'etichetta singola del tipo di articolo"

#: inc/class-wpseo-replace-vars.php:1485
msgid "Replaced with the current search phrase"
msgstr "Sostituito con la frase di ricerca corrente"

#: inc/class-wpseo-replace-vars.php:1484
msgid "Replaced with the term name"
msgstr "Sostituito dal nome del termine"

#: inc/class-wpseo-replace-vars.php:1483
msgid "Replaced with the term description"
msgstr "Sostituito dalla descrizione del termine"

#: inc/class-wpseo-replace-vars.php:1482
msgid "Replaced with the tag description"
msgstr "Sostituito dalla descrizione del tag"

#: inc/class-wpseo-replace-vars.php:1481
msgid "Replaced with the category description"
msgstr "Sostituito dalla descrizione della categoria"

#: inc/class-wpseo-replace-vars.php:1479
msgid "Replaced with the post categories (comma separated)"
msgstr "Sostituito dalle categorie dell'articolo (separate da una virgola)"

#: inc/class-wpseo-replace-vars.php:1478
msgid "Replaced with the current tag/tags"
msgstr "Rimpiazzato dagli attuali tag"

#: inc/class-wpseo-replace-vars.php:1477
msgid "Replaced with the post/page excerpt (without auto-generation)"
msgstr "Sostituito dal riassunto articolo/pagina (senza autogenerazione)"

#: inc/class-wpseo-replace-vars.php:1476
msgid "Replaced with the post/page excerpt (or auto-generated if it does not exist)"
msgstr "Sostituito dal riassunto articolo/pagina (autogenerato se non esiste)"

#: inc/class-wpseo-replace-vars.php:1474
msgid "The site's name"
msgstr "Il nome del sito"

#: inc/class-wpseo-replace-vars.php:1472
msgid "Replaced with the title of the parent page of the current page"
msgstr "Sostituito con il titolo della pagina padre della pagina corrente"

#: inc/class-wpseo-replace-vars.php:1471
msgid "Replaced with the title of the post/page"
msgstr "Sostituito dal titolo del post/pagina"

#: inc/class-wpseo-replace-vars.php:1470
msgid "Replaced with the date of the post/page"
msgstr "Rimpiazzato dalla data articolo/pagina"

#. translators: 1: current page number, 2: total number of pages.
#: inc/class-wpseo-replace-vars.php:1029
msgid "Page %1$d of %2$d"
msgstr "Pagina %1$d di %2$d"

#: inc/class-wpseo-replace-vars.php:124
msgid "You cannot overrule a WPSEO standard variable replacement by registering a variable with the same name. Use the \"wpseo_replacements\" filter instead to adjust the replacement value."
msgstr "Non puoi sovrascrivere una variabile di sostituzione standard di WPSEO registrando una variabile con lo stesso nome. Utilizza piuttosto il filtro \"wpseo_replacements\" per aggiustare il valore di sostituzione."

#: inc/class-wpseo-replace-vars.php:120
msgid "A replacement variable with the same name has already been registered. Try making your variable name unique."
msgstr "Una variabile di sostituzione con questo nome è già stata registrata. Prova a rendere il nome di questa variabile ancora più univoco."

#: inc/class-wpseo-replace-vars.php:110
msgid "A replacement variable can not start with \"%%cf_\" or \"%%ct_\" as these are reserved for the WPSEO standard variable variables for custom fields and custom taxonomies. Try making your variable name unique."
msgstr "Una variabile di sostituzione non può iniziare con \"%%cf_\" o \"%%ct_\" perchè sono riservate per le variabili di variabili standard di WPSEO per i campi personalizzati e per le tassonomie personalizzate. Prova a rendere il nome di questa variabile univoco."

#: inc/class-wpseo-replace-vars.php:107
msgid "A replacement variable can only contain alphanumeric characters, an underscore or a dash. Try renaming your variable."
msgstr "Una variabile di sostituzione può contenere solo caratteri alfanumerici, il carattere di sottolineatura o il trattino. Prova a rinoinare la tua variabile."

#. translators: %1$s resolves to Yoast SEO, %2$s resolves to the Settings
#. submenu item.
#: src/presenters/meta-description-presenter.php:35
msgid "Admin only notice: this page does not show a meta description because it does not have one, either write it for this page specifically or go into the [%1$s - %2$s] menu and set up a template."
msgstr "Avviso per l'amministratore: questa pagina non mostra una meta descrizione perché non ne ha una, è necessario scriverla per questa pagina o andare nel menu [%1$s - %2$s] e impostare un template."

#: inc/options/class-wpseo-option-titles.php:275
msgid "Page not found"
msgstr "Pagina non trovata"

#. translators: %s expands to the variable used for term title.
#: inc/class-upgrade.php:1558 inc/options/class-wpseo-option-titles.php:345
#: src/editors/framework/seo/terms/title-data-provider.php:27
msgid "%s Archives"
msgstr "%s Archivi"

#: admin/views/user-profile.php:30
#: src/user-meta/framework/custom-meta/author-metadesc.php:97
msgid "Meta description to use for Author page"
msgstr "Meta descrizione da usare per la pagina autore "

#: admin/views/user-profile.php:26
#: src/user-meta/framework/custom-meta/author-title.php:97
msgid "Title to use for Author page"
msgstr "Titolo da usare per la pagina autore"

#. translators: %1$s expands to Yoast SEO
#: admin/views/user-profile.php:13
#: src/user-meta/user-interface/custom-meta-integration.php:100
msgid "%1$s settings"
msgstr "Impostazioni %1$s"

#. translators: %1$s expands to Yoast SEO
#: admin/views/tabs/tool/wpseo-export.php:15
msgid "Export your %1$s settings"
msgstr "Esporta le tue impostazioni %1$s"

#: admin/class-export.php:65 admin/views/tabs/tool/wpseo-import.php:25
#: admin/views/tabs/tool/wpseo-import.php:45
#: admin/views/tool-import-export.php:86
msgid "Import settings"
msgstr "Importa impostazioni"

#: admin/views/tabs/tool/import-seo.php:18
#: admin/views/tabs/tool/import-seo.php:49
#: admin/views/tool-import-export.php:92
msgid "Import from other SEO plugins"
msgstr "Importa da altri plugin SEO"

#: admin/views/tabs/tool/import-seo.php:88
#: admin/views/tool-import-export.php:24
msgid "Import"
msgstr "Importa"

#. translators: %s expands to robots.txt.
#. translators: %s expands to ".htaccess".
#: admin/views/tool-file-editor.php:135 admin/views/tool-file-editor.php:235
msgid "If you had a %s file and it was editable, you could edit it from here."
msgstr "Se tu avessi un file %s e questo fosse modificabile, potresti modificarlo da qui."

#. translators: %s expands to robots.txt.
#. translators: %s expands to ".htaccess".
#: admin/views/tool-file-editor.php:153 admin/views/tool-file-editor.php:203
msgid "If your %s were writable, you could edit it from here."
msgstr "Se il tuo %s fosse modificabile, potresti modificarlo da qui."

#. translators: %s expands to robots.txt.
#: admin/views/tool-file-editor.php:116
msgid "You don't have a %s file, create one here:"
msgstr "Non hai un file %s, creane uno qui:"

#: admin/statistics/class-statistics-service.php:80
msgid "Below are your published posts' SEO scores. Now is as good a time as any to start improving some of your posts!"
msgstr "Di seguito sono elencati i punteggi SEO dei tuoi articoli pubblicati. Ora è il momento giusto per iniziare a migliorare alcuni dei tuoi articoli!"

#: admin/views/tabs/dashboard/dashboard.php:40
msgid "Credits"
msgstr "Crediti"

#: admin/pages/tools.php:77
msgid "&laquo; Back to Tools page"
msgstr "&laquo; Torna alla pagina degli strumenti"

#. translators: %1$s expands to Yoast SEO
#: admin/pages/tools.php:49
msgid "%1$s comes with some very powerful built-in tools:"
msgstr "%1$s viene fornito con alcuni strumenti integrati molto potenti:"

#: admin/pages/tools.php:37
msgid "This tool allows you to quickly change important files for your SEO, like your robots.txt and, if you have one, your .htaccess file."
msgstr "Questo strumento consente di modificare rapidamente file importanti per la SEO, come il robots.txt e, se ne hai uno, il file .htaccess."

#: admin/pages/tools.php:36
msgid "File editor"
msgstr "Modifica file"

#: admin/pages/tools.php:31
msgid "Import settings from other SEO plugins and export your settings for re-use on (another) site."
msgstr "Importa le impostazioni da altri plugin SEO ed esporta le tue impostazione per riutilizzarle su un altro sito."

#: admin/pages/tools.php:30
msgid "Import and Export"
msgstr "Importa ed esporta"

#: admin/pages/tools.php:43
msgid "This tool allows you to quickly change titles and descriptions of your posts and pages without having to go into the editor for each page."
msgstr "Questo strumento permette di cambiare rapidamente i titoli e le descrizioni dei tuoi articoli e pagine senza dover andare nell'editor per ognuno di essi."

#: admin/pages/tools.php:42
msgid "Bulk editor"
msgstr "Editor di massa"

#: src/integrations/admin/import-integration.php:119
msgid "Default settings"
msgstr "Impostazioni standard"

#: admin/views/tool-bulk-editor.php:113 js/dist/new-settings.js:256
msgid "Description"
msgstr "Descrizione"

#: admin/views/tabs/network/restore-site.php:32
msgid "Restore site to defaults"
msgstr "Reimposta il sito"

#: admin/views/tabs/network/restore-site.php:23
#: admin/views/tabs/network/restore-site.php:28
msgid "Site ID"
msgstr "ID Sito"

#: admin/views/tabs/network/restore-site.php:16
msgid "Using this form you can reset a site to the default SEO settings."
msgstr "Usando questo modulo sarà possibile impostare il sito con i valori predefiniti di SEO."

#: admin/views/tabs/network/general.php:54
msgid "Privacy sensitive (FB admins and such), theme specific (title rewrite) and a few very site specific settings will not be imported to new sites."
msgstr "I dati sensibili della privacy (amministratori FB e simili), specifici per il tema (riscrittura titolo) ed alcune altre impostazioni specifiche non verranno importate nei nuovi siti."

#. translators: 1: link open tag; 2: link close tag.
#: admin/views/tabs/network/general.php:47
msgid "Enter the %1$sSite ID%2$s for the site whose settings you want to use as default for all sites that are added to your network. Leave empty for none (i.e. the normal plugin defaults will be used)."
msgstr "Inserisci lo %1$sID del sito%2$s per il sito dal quale ricavare le impostazioni che desideri utilizzare come predefinite per tutti i siti già aggiunti al tuo network. Lascia vuoto per nessun sito (verranno utilizzati i valori predefiniti del plugin)."

#: admin/views/tabs/network/general.php:40
msgid "Choose the site whose settings you want to use as default for all sites that are added to your network. If you choose 'None', the normal plugin defaults will be used."
msgstr "Scegli il sito le cui impostazioni verranno utilizzate come standard per tutti i siti che verranno aggiunti al network. Se scegli 'Nessuno', verranno utilizzati i valori standard del plugin."

#: admin/views/tabs/network/general.php:37
#: admin/views/tabs/network/general.php:43
msgid "New sites in the network inherit their SEO settings from this site"
msgstr "I nuovi siti del network erediteranno le impostazioni SEO da questo sito"

#: admin/views/tabs/network/general.php:28
msgid "Super Admins only"
msgstr "Solo super amministratori"

#: admin/views/tabs/network/general.php:27
msgid "Site Admins (default)"
msgstr "Amministratori del sito (predefinito)"

#. translators: %1$s expands to Yoast SEO
#: admin/views/tabs/network/general.php:25
msgid "Who should have access to the %1$s settings"
msgstr "Chi dovrebbe avere accesso alle impostazioni di %1$s"

#: admin/class-yoast-network-admin.php:80
msgid "spam"
msgstr "spam"

#: admin/class-yoast-network-admin.php:79
msgid "mature"
msgstr "maturo"

#: admin/class-yoast-network-admin.php:78
msgid "archived"
msgstr "archiviato"

#: admin/class-yoast-network-admin.php:77
msgid "public"
msgstr "pubblico"

#. translators: %s expands to the name of a site within a multisite network.
#: admin/class-yoast-network-admin.php:174
msgid "%s restored to default SEO settings."
msgstr "%s ripristinato ai valori di default SEO."

#: admin/class-yoast-network-admin.php:140
msgid "Settings Updated."
msgstr "Impostazioni aggiornate."

#: admin/views/tool-bulk-editor.php:111 inc/class-wpseo-replace-vars.php:1471
#: js/dist/elementor.js:540 js/dist/externals-redux.js:1
msgid "Title"
msgstr "Titolo"

#: admin/views/tabs/network/general.php:54
msgid "Take note:"
msgstr "Prendi nota:"

#. translators: %1$s expands to Yoast SEO
#: admin/class-plugin-availability.php:89
msgid "Seamlessly integrate WooCommerce with %1$s and get extra features!"
msgstr "Integra al meglio WooCommerce con %1$s ed ottieni funzionalità extra!"

#: admin/class-plugin-availability.php:77
msgid "Rank better locally and in Google Maps, without breaking a sweat!"
msgstr "Migliora il posizionamento locale e su Google Maps, senza fare nessuno sforzo!"

#: admin/class-plugin-availability.php:67
msgid "Are you in Google News? Increase your traffic from Google News by optimizing for it!"
msgstr "Sei su Google News? Aumenta il tuo traffico da Google News ottimizzandoti per esso!"

#: admin/class-plugin-availability.php:57
msgid "Optimize your videos to show them off in search results and get more clicks!"
msgstr "Ottimizza i tuoi video per visualizzarli nei risultati di ricerca ed avere più click!"

#. translators: %1$s expands to Yoast SEO
#: admin/class-plugin-availability.php:45
msgid "The premium version of %1$s with more features & support."
msgstr "La versione premium di %1$s con molte altre funzionalità e l'assistenza."

#: src/integrations/admin/first-time-configuration-integration.php:485
#: js/dist/new-settings.js:324
msgid "Person"
msgstr "Persona"

#: inc/class-wpseo-replace-vars.php:1497
msgid "Permalink"
msgstr "Permalink"

#. Translators: %1$s: expands to 'Yoast SEO Premium', %2$s: links to Yoast SEO
#. Premium plugin page.
#: admin/google_search_console/views/gsc-redirect-nopremium.php:22
msgid "To be able to create a redirect and fix this issue, you need %1$s. You can buy the plugin, including one year of support and updates, on %2$s."
msgstr "Per essere in grado di creare un redirect e risolvere questo problema, hai bisogno di %1$s. È possibile acquistare il plugin, compreso un anno di assistenza e aggiornamenti, su %2$s."

#. Translators: %s: expands to Yoast SEO Premium
#: admin/google_search_console/views/gsc-redirect-nopremium.php:15
msgid "Creating redirects is a %s feature"
msgstr "Creare redirect è una caratteristica di %s"

#: admin/views/redirects.php:175 admin/views/redirects.php:222
msgid "New URL"
msgstr "Nuovo URL"

#: admin/views/redirects.php:112
msgid "URL"
msgstr "URL"

#. translators: %s: 'Facebook' plugin name of possibly conflicting plugin
#: admin/class-yoast-plugin-conflict.php:186
msgid "Deactivate %s"
msgstr "Disattivare %s"

#. translators: %1$s: 'Facebook & Open Graph' plugin name(s) of possibly
#. conflicting plugin(s), %2$s to Yoast SEO
#: admin/class-yoast-plugin-conflict.php:182
msgid "The %1$s plugin might cause issues when used in conjunction with %2$s."
msgstr "I plugin %1$s potrebbero causare problemi quando utilizzati in combinazione con %2$s."

#. translators: %s is the plugin name
#: admin/class-yoast-dashboard-widget.php:79
msgid "%s Posts Overview"
msgstr "Panoramica degli articoli %s"

#: admin/google_search_console/views/gsc-redirect-nopremium.php:27
#: js/dist/block-editor.js:19 js/dist/block-editor.js:23
#: js/dist/classic-editor.js:4 js/dist/classic-editor.js:8
#: js/dist/elementor.js:4 js/dist/elementor.js:8
#: js/dist/externals-components.js:191 js/dist/externals-components.js:195
#: js/dist/externals-components.js:256 js/dist/externals/componentsNew.js:790
#: js/dist/general-page.js:6 js/dist/general-page.js:10
#: js/dist/general-page.js:22 js/dist/general-page.js:23
#: js/dist/general-page.js:36 js/dist/integrations-page.js:40
#: js/dist/integrations-page.js:44 js/dist/integrations-page.js:56
#: js/dist/integrations-page.js:57 js/dist/introductions.js:4
#: js/dist/new-settings.js:6 js/dist/new-settings.js:10
#: js/dist/new-settings.js:22 js/dist/new-settings.js:45 js/dist/post-edit.js:4
#: js/dist/post-edit.js:8 js/dist/support.js:6 js/dist/support.js:10
#: js/dist/term-edit.js:4 js/dist/term-edit.js:8
msgid "Close"
msgstr "Chiudi"

#. translators: %1$s expands to Yoast SEO, %2$s: 'Google XML Sitemaps' plugin
#. name of possibly conflicting plugin with regard to the creation of sitemaps.
#: admin/class-plugin-conflict.php:78
msgid "Both %1$s and %2$s can create XML sitemaps. Having two XML sitemaps is not beneficial for search engines and might slow down your site."
msgstr "Sia %1$s che %2$s generano una mappa del sito XML. Avere due file XML non porta benefici per i motori di ricerca e potrebbe rallentare il sito."

#. translators: %1$s expands to Yoast SEO.
#: admin/class-plugin-conflict.php:71
msgid "Configure %1$s's Open Graph settings"
msgstr "Configura le impostazioni OpenGraph di %1$s"

#. translators: %1$s expands to Yoast SEO, %2$s: 'Facebook' plugin name of
#. possibly conflicting plugin with regard to creating OpenGraph output.
#: admin/class-plugin-conflict.php:67
msgid "Both %1$s and %2$s create Open Graph output, which might make Facebook, X, LinkedIn and other social networks use the wrong texts and images when your pages are being shared."
msgstr "Sia %1$s sia %2$s generano dei meta tag OpenGraph, che potrebbero causare immagini e testi sbagliati qualora fossero condivisi su Facebook, X, LinkedIn ed altri social network."

#: admin/statistics/class-statistics-service.php:222
#: inc/class-wpseo-rank.php:141 inc/class-wpseo-rank.php:181
#: inc/class-wpseo-rank.php:213 js/dist/block-editor.js:540
#: js/dist/editor-modules.js:315 js/dist/elementor.js:94
#: js/dist/externals-components.js:188 js/dist/externals/analysis.js:15
#: js/dist/externals/dashboardFrontend.js:4
#: js/dist/frontend-inspector-resources.js:1 js/dist/post-edit.js:25
#: js/dist/term-edit.js:1
msgid "OK"
msgstr "OK"

#: admin/class-meta-columns.php:129
msgid "Meta Desc."
msgstr "Desc. meta"

#: admin/class-meta-columns.php:273
msgid "All SEO Scores"
msgstr "Tutti i punteggi SEO"

#: admin/class-meta-columns.php:828
msgid "Post is set to noindex."
msgstr "Articolo impostato come da non indicizzare"

#: admin/metabox/class-metabox.php:212
msgid "The URL that this page should redirect to."
msgstr "L'URL al quale questa pagina deve reindirizzare."

#: admin/metabox/class-metabox.php:211
msgid "301 Redirect"
msgstr "Redirect 301"

#. translators: 1: link open tag; 2: link close tag.
#: admin/metabox/class-metabox.php:206
msgid "The canonical URL that this page should point to. Leave empty to default to permalink. %1$sCross domain canonical%2$s supported too."
msgstr "L'URL canonico a cui questa pagina dovrebbe puntare. Lascia in bianco per utilizzare il permalink predefinito. Sono supportate anche gli %1$sURL canonici tra domini diversi (cross domain)%2$s."

#: admin/metabox/class-metabox.php:202 js/dist/block-editor.js:291
#: js/dist/classic-editor.js:276 js/dist/elementor.js:399
msgid "Canonical URL"
msgstr "URL canonico"

#: admin/metabox/class-metabox.php:200
msgid "Title to use for this page in breadcrumb paths"
msgstr "Titolo da usare nel percorso del breadcrumb in questa pagina"

#: admin/metabox/class-metabox.php:199 js/dist/block-editor.js:290
#: js/dist/classic-editor.js:275 js/dist/elementor.js:398
msgid "Breadcrumbs Title"
msgstr "Titolo dei breadcrumbs"

#: admin/metabox/class-metabox.php:197 js/dist/block-editor.js:289
#: js/dist/classic-editor.js:274 js/dist/elementor.js:397
msgid "No Snippet"
msgstr "Nessuno snippet"

#: admin/metabox/class-metabox.php:196 js/dist/block-editor.js:289
#: js/dist/classic-editor.js:274 js/dist/elementor.js:397
msgid "No Archive"
msgstr "Nessun Archivio"

#: admin/metabox/class-metabox.php:195 js/dist/block-editor.js:289
#: js/dist/classic-editor.js:274 js/dist/elementor.js:397
msgid "No Image Index"
msgstr "Nessuna immagine indice"

#: admin/class-yoast-network-admin.php:43 src/config/schema-types.php:163
#: src/integrations/settings-integration.php:581 js/dist/new-settings.js:23
#: js/dist/new-settings.js:356
msgid "None"
msgstr "Nessuno"

#: admin/metabox/class-metabox.php:193 js/dist/block-editor.js:289
#: js/dist/classic-editor.js:274 js/dist/elementor.js:397
msgid "Meta robots advanced"
msgstr "Meta Robots avanzate"

#: admin/metabox/class-metabox.php:181
msgid "Warning: even though you can set the meta robots setting here, the entire site is set to noindex in the sitewide privacy settings, so these settings won't have an effect."
msgstr "Attenzione: anche se da qui puoi impostare i valori meta per le impostazioni per i robot, l'intero sito è impostato come da non indicizzare nelle impostazioni generali di privacy, quindi queste impostazioni non avranno alcun effetto."

#: admin/metabox/class-metabox.php:176
#: js/dist/externals/replacementVariableEditor.js:27
#: js/dist/externals/searchMetadataPreviews.js:62 js/dist/new-settings.js:31
#: js/dist/new-settings.js:35 js/dist/new-settings.js:38
#: js/dist/new-settings.js:42 js/dist/new-settings.js:68
#: js/dist/new-settings.js:82 js/dist/new-settings.js:112
#: js/dist/new-settings.js:141 js/dist/new-settings.js:206
#: js/dist/new-settings.js:223 js/dist/new-settings.js:232
#: js/dist/new-settings.js:254
msgid "Meta description"
msgstr "Meta descrizione"

#: admin/class-meta-columns.php:128
msgid "SEO Title"
msgstr "Titolo SEO"

#: inc/class-wpseo-replace-vars.php:1524
msgid "Focus keyword"
msgstr "Parola chiave principale"

#: admin/import/class-import-settings.php:121
msgid "Settings successfully imported."
msgstr "Impostazioni importate con successo."

#: admin/import/class-import-settings.php:85
msgid "Settings could not be imported:"
msgstr "Le impostazioni non possono essere importate:"

#: admin/class-bulk-editor-list-table.php:1045
msgid "Action"
msgstr "Azione"

#: admin/class-bulk-editor-list-table.php:1040
msgid "Page URL/Slug"
msgstr "URL/Slug pagina"

#: admin/class-bulk-editor-list-table.php:1039
msgid "Publication date"
msgstr "Data di pubblicazione"

#: admin/class-bulk-editor-list-table.php:1038
msgid "Post Status"
msgstr "Stato articolo"

#: admin/class-bulk-editor-list-table.php:1036
msgid "WP Page Title"
msgstr "Titolo pagina WP"

#: admin/class-bulk-editor-list-table.php:866 js/dist/block-editor.js:149
#: js/dist/classic-editor.js:134 js/dist/editor-modules.js:270
#: js/dist/elementor.js:489 js/dist/externals/dashboardFrontend.js:5
#: js/dist/wincher-dashboard-widget.js:43
#: js/dist/wincher-dashboard-widget.js:112
msgid "View"
msgstr "Visualizza"

#. translators: Hidden accessibility text; %s: post title.
#: admin/class-bulk-editor-list-table.php:865
msgid "View &#8220;%s&#8221;"
msgstr "Visualizza &#8220;%s&#8221;"

#: admin/class-bulk-editor-list-table.php:856
msgid "Preview"
msgstr "Anteprima"

#. translators: Hidden accessibility text; %s: post title.
#: admin/class-bulk-editor-list-table.php:855
msgid "Preview &#8220;%s&#8221;"
msgstr "Anteprima &#8220;%s&#8221;"

#: admin/class-bulk-editor-list-table.php:844
#: js/dist/externals/dashboardFrontend.js:4 js/dist/general-page.js:48
#: js/dist/general-page.js:55
msgid "Edit"
msgstr "Modifica"

#: admin/class-bulk-editor-list-table.php:438 admin/views/redirects.php:141
msgid "Filter"
msgstr "Filtro"

#. translators: %s expands to the number of trashed posts in localized format.
#: admin/class-bulk-editor-list-table.php:357
msgctxt "posts"
msgid "Trash <span class=\"count\">(%s)</span>"
msgid_plural "Trash <span class=\"count\">(%s)</span>"
msgstr[0] "Cestinato <span class=\"count\">(%s)</span>"
msgstr[1] "Cestinati <span class=\"count\">(%s)</span>"

#. translators: %s expands to the number of posts in localized format.
#: admin/class-bulk-editor-list-table.php:308
msgctxt "posts"
msgid "All <span class=\"count\">(%s)</span>"
msgid_plural "All <span class=\"count\">(%s)</span>"
msgstr[0] "Tutto <span class=\"count\">(%s)</span>"
msgstr[1] "Tutti <span class=\"count\">(%s)</span>"

#: admin/class-bulk-description-editor-list-table.php:47
msgid "New Yoast Meta Description"
msgstr "Nuova meta descrizione Yoast"

#: admin/class-bulk-description-editor-list-table.php:46
msgid "Existing Yoast Meta Description"
msgstr "Meta descrizione Yoast esistente"

#: admin/class-admin.php:308
#: src/user-meta/framework/additional-contactmethods/facebook.php:28
msgid "Facebook profile URL"
msgstr "URL profilo Facebook"

#: admin/class-admin.php:229
msgid "FAQ"
msgstr "FAQ"

#: admin/class-admin.php:224 admin/views/redirects.php:42
#: src/integrations/settings-integration.php:325
#: src/presenters/meta-description-presenter.php:37 js/dist/how-to-block.js:11
msgid "Settings"
msgstr "Impostazioni"

#: src/presenters/admin/search-engines-discouraged-presenter.php:33
msgid "Huge SEO Issue: You're blocking access to robots."
msgstr "Grande problema SEO: stai bloccando l'accesso ai robot."

#: admin/class-admin.php:182
msgid "Posts"
msgstr "Articoli"

#: admin/menu/class-network-admin-menu.php:63
msgid "Edit Files"
msgstr "Modifica Files"

#: admin/menu/class-network-admin-menu.php:56 admin/pages/network.php:18
#: src/general/user-interface/general-page-integration.php:158
#: js/dist/new-settings.js:354
msgid "General"
msgstr "Generale"

#: admin/menu/class-admin-menu.php:92
msgid "Search Console"
msgstr "Console di ricerca"

#: admin/menu/class-admin-menu.php:96 js/dist/new-settings.js:312
msgid "Tools"
msgstr "Strumenti"

#: admin/views/class-yoast-feature-toggles.php:136 js/dist/new-settings.js:38
#: js/dist/new-settings.js:314 js/dist/new-settings.js:316
msgid "XML sitemaps"
msgstr "Sitemaps XML"

#: admin/metabox/class-metabox.php:435
#: admin/taxonomy/class-taxonomy-metabox.php:160 js/dist/new-settings.js:42
msgid "Social"
msgstr "Social"

#: admin/metabox/class-metabox.php:410
#: admin/taxonomy/class-taxonomy-metabox.php:142
#: inc/class-wpseo-admin-bar-menu.php:715
#: src/services/health-check/report-builder.php:168
msgid "SEO"
msgstr "SEO"

#. translators: %1$s expands to Yoast SEO, %2$s expands to the installed
#. version, %3$s expands to Gutenberg
#: admin/class-admin-gutenberg-compatibility-notification.php:88
msgid "%1$s detected you are using version %2$s of %3$s, please update to the latest version to prevent compatibility issues."
msgstr "%1$s ha scoperto che stai utilizzando la versione %2$s di %3$s, aggiorna all'ultima versione per evitare problemi di compatibilità."

#: src/services/health-check/default-tagline-runner.php:32
msgid "Just another WordPress site"
msgstr "Solo un altro sito WordPress"

#: admin/ajax.php:206
msgid "You have used HTML in your value which is not allowed."
msgstr "Non è consentito utilizzare HTML."

#. translators: %s expands to the name of a post type (plural).
#: admin/ajax.php:197
msgid "You can't edit %s that aren't yours."
msgstr "Non puoi modificare i %s che non sono tuoi."

#. translators: %s expands to post type name.
#: admin/ajax.php:185
msgid "You can't edit %s."
msgstr "Non puoi modificare %s."

#. translators: %s expands to post type.
#: admin/ajax.php:173
msgid "Post has an invalid Content Type: %s."
msgstr "L'articolo ha impostato un tipo invalido: %s."

#: admin/ajax.php:162
msgid "Post doesn't exist."
msgstr "L'articolo non esiste."