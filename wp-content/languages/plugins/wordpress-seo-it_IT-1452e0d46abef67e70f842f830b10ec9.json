{"translation-revision-date": "2025-06-30 10:07:57+0000", "generator": "GlotPress/4.0.1", "domain": "messages", "locale_data": {"messages": {"": {"domain": "messages", "plural-forms": "nplurals=2; plural=n != 1;", "lang": "it"}, "%1$s, %2$s and %3$s": ["%1$s, %2$s e %3$s"], "%1$s and %2$s": ["%1$s e %2$s"], "New step added": ["È stato aggiunto un nuovo passaggio"], "Optional. Customize how you want to describe the duration of the instruction": ["Opzionale. Personalizza come vuoi descrivere la durata del processo indicato nelle istruzioni:"], "Describe the duration of the instruction:": ["Descrivi la durata del processo indicato nelle istruzioni:"], "%d minute": ["%d minuto", "%d minuti"], "%d hour": ["%d ora", "%d ore"], "%d day": ["%d giorno", "%d giorni"], "Enter a step title": ["Inserisci un titolo del passaggio "], "Optional. This can give you better control over the styling of the steps.": ["Opzionale. Questo ti fornisce un controllo migliore dello stile dei passaggi. "], "CSS class(es) to apply to the steps": ["Classe(i) CSS da applicare ai passaggi "], "minutes": ["minuti "], "hours": ["ore"], "days": ["<PERSON>ior<PERSON>"], "Time needed:": ["Tempo richiesto:"], "Move step down": ["<PERSON><PERSON><PERSON> passaggio in basso"], "Move step up": ["<PERSON><PERSON>a passaggio in alto"], "Insert step": ["<PERSON>ser<PERSON><PERSON> passaggio"], "Delete step": ["Elimina passaggio"], "Add image": ["Aggiungi un'immagine"], "Enter a step description": ["Inserisci una descrizione del passaggio"], "Enter a description": ["Inserisci una descrizione"], "Unordered list": ["Lista non ordinata"], "Showing step items as an ordered list.": ["Mostra gli elementi del passaggio come un elenco ordinato."], "Showing step items as an unordered list": ["Mostra gli elementi del passaggio come un elenco non ordinato"], "Add step": ["Aggiungi un passaggio"], "Delete total time": ["Elimina il tempo totale"], "Add total time": ["Aggiungi il tempo totale"], "Settings": ["Impostazioni"]}}, "comment": {"reference": "js/dist/how-to-block.js"}}