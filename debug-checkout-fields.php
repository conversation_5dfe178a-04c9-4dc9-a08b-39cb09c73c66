<?php
/**
 * Debug script to check WooCommerce checkout fields
 * Add this to functions.php temporarily to debug
 */

// Debug checkout fields
add_action('wp_footer', function() {
    if (is_checkout()) {
        $checkout = WC()->checkout();
        $billing_fields = $checkout->get_checkout_fields('billing');
        
        echo '<script>console.log("=== BILLING FIELDS DEBUG ===");</script>';
        
        foreach ($billing_fields as $key => $field) {
            $hidden = isset($field['hidden']) && $field['hidden'] ? 'HIDDEN' : 'VISIBLE';
            $required = isset($field['required']) && $field['required'] ? 'REQUIRED' : 'OPTIONAL';
            
            echo '<script>console.log("' . $key . ': ' . $hidden . ' - ' . $required . '");</script>';
            
            if ($key === 'billing_country' || $key === 'billing_state') {
                echo '<script>console.log("' . $key . ' field details:", ' . json_encode($field) . ');</script>';
            }
        }
        
        echo '<script>console.log("=== END BILLING FIELDS DEBUG ===");</script>';
    }
});
